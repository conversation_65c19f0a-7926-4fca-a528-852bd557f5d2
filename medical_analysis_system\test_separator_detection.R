# 测试分隔符检测功能
# Test separator detection functionality

# 设置工作目录
if (!file.exists("global.R")) {
  stop("请在medical_analysis_system目录下运行此脚本")
}

# 加载必要模块
source("global.R")

cat("=== 分隔符检测功能测试 ===\n\n")

# 创建测试数据
create_test_files_with_different_separators <- function() {
  # 创建测试数据
  test_data <- data.frame(
    icustay_id = 1:10,
    age = sample(20:80, 10),
    gender = sample(c("M", "F"), 10, replace = TRUE),
    score = round(rnorm(10, 50, 10), 1),
    stringsAsFactors = FALSE
  )
  
  # 创建测试目录
  if (!dir.exists("data/separator_test")) {
    dir.create("data/separator_test", recursive = TRUE)
  }
  
  # 保存不同分隔符的文件
  write.csv(test_data, "data/separator_test/comma_separated.csv", row.names = FALSE)
  write.table(test_data, "data/separator_test/tab_separated.tsv", sep = "\t", row.names = FALSE, quote = FALSE)
  write.table(test_data, "data/separator_test/semicolon_separated.txt", sep = ";", row.names = FALSE, quote = FALSE)
  write.table(test_data, "data/separator_test/pipe_separated.txt", sep = "|", row.names = FALSE, quote = FALSE)
  write.table(test_data, "data/separator_test/space_separated.txt", sep = " ", row.names = FALSE, quote = FALSE)
  
  cat("✅ 创建了以下测试文件:\n")
  cat("   - comma_separated.csv (逗号分隔)\n")
  cat("   - tab_separated.tsv (制表符分隔)\n")
  cat("   - semicolon_separated.txt (分号分隔)\n")
  cat("   - pipe_separated.txt (竖线分隔)\n")
  cat("   - space_separated.txt (空格分隔)\n\n")
  
  return(test_data)
}

# 测试分隔符检测函数
test_separator_detection <- function() {
  cat("🔍 测试分隔符自动检测功能...\n\n")
  
  # 测试文件列表
  test_files <- list(
    list(file = "data/separator_test/comma_separated.csv", expected = ","),
    list(file = "data/separator_test/tab_separated.tsv", expected = "\t"),
    list(file = "data/separator_test/semicolon_separated.txt", expected = ";"),
    list(file = "data/separator_test/pipe_separated.txt", expected = "|"),
    list(file = "data/separator_test/space_separated.txt", expected = " ")
  )
  
  results <- list()
  
  for (i in seq_along(test_files)) {
    test_file <- test_files[[i]]
    file_path <- test_file$file
    expected_sep <- test_file$expected
    
    if (file.exists(file_path)) {
      tryCatch({
        # 测试自动检测
        detected_sep <- detect_separator(file_path, "auto")
        
        # 检查结果
        is_correct <- detected_sep == expected_sep
        status <- if (is_correct) "✅ 正确" else "❌ 错误"
        
        cat(sprintf("文件: %s\n", basename(file_path)))
        cat(sprintf("  期望分隔符: %s\n", if (expected_sep == "\t") "\\t" else expected_sep))
        cat(sprintf("  检测分隔符: %s\n", if (detected_sep == "\t") "\\t" else detected_sep))
        cat(sprintf("  检测结果: %s\n\n", status))
        
        results[[i]] <- list(
          file = basename(file_path),
          expected = expected_sep,
          detected = detected_sep,
          correct = is_correct
        )
        
      }, error = function(e) {
        cat(sprintf("文件: %s\n", basename(file_path)))
        cat(sprintf("  错误: %s\n\n", e$message))
        
        results[[i]] <- list(
          file = basename(file_path),
          expected = expected_sep,
          detected = NA,
          correct = FALSE,
          error = e$message
        )
      })
    } else {
      cat(sprintf("⚠️ 文件不存在: %s\n\n", file_path))
    }
  }
  
  return(results)
}

# 测试文件描述生成
test_description_generation <- function() {
  cat("📝 测试自动描述生成功能...\n\n")
  
  # 创建不同类型的测试数据
  test_datasets <- list(
    basic_info = data.frame(
      icustay_id = 1:5,
      age = c(65, 72, 58, 81, 69),
      gender = c("M", "F", "M", "F", "M"),
      stringsAsFactors = FALSE
    ),
    lab_data = data.frame(
      icustay_id = 1:5,
      hemoglobin = c(12.5, 10.8, 13.2, 11.9, 12.1),
      creatinine = c(1.2, 2.1, 0.9, 1.5, 1.0),
      stringsAsFactors = FALSE
    ),
    vital_signs = data.frame(
      icustay_id = 1:5,
      heart_rate = c(80, 95, 72, 88, 76),
      systolic_bp = c(120, 140, 110, 135, 125),
      stringsAsFactors = FALSE
    )
  )
  
  # 测试文件名
  test_filenames <- c(
    "patient_basic_info.csv",
    "laboratory_results.csv", 
    "vital_signs_data.csv",
    "unknown_data.csv"
  )
  
  for (i in seq_along(test_filenames)) {
    filename <- test_filenames[i]
    
    # 选择对应的数据集
    if (i <= length(test_datasets)) {
      data <- test_datasets[[i]]
    } else {
      data <- test_datasets[[1]]  # 使用默认数据
    }
    
    description <- generate_auto_description(filename, data)
    
    cat(sprintf("文件名: %s\n", filename))
    cat(sprintf("生成描述: %s\n", description))
    cat(sprintf("数据列: %s\n\n", paste(names(data), collapse = ", ")))
  }
}

# 运行所有测试
run_all_tests <- function() {
  cat("开始运行分隔符检测和描述生成测试...\n\n")
  
  # 创建测试文件
  original_data <- create_test_files_with_different_separators()
  
  # 测试分隔符检测
  detection_results <- test_separator_detection()
  
  # 测试描述生成
  test_description_generation()
  
  # 汇总结果
  cat("📊 测试结果汇总:\n")
  correct_count <- sum(sapply(detection_results, function(x) x$correct), na.rm = TRUE)
  total_count <- length(detection_results)
  
  cat(sprintf("分隔符检测准确率: %d/%d (%.1f%%)\n", 
              correct_count, total_count, correct_count/total_count*100))
  
  if (correct_count == total_count) {
    cat("🎉 所有测试通过！分隔符检测功能正常工作。\n")
  } else {
    cat("⚠️ 部分测试失败，请检查分隔符检测算法。\n")
  }
  
  cat("\n测试完成！您现在可以启动应用测试完整的多文件上传功能。\n")
}

# 如果直接运行此脚本，执行所有测试
if (interactive()) {
  run_all_tests()
} else {
  cat("测试脚本已加载。运行 run_all_tests() 来执行所有测试。\n")
}

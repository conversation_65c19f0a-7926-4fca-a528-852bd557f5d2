# 测试增强功能
# Test Enhanced Features

cat("=== 增强功能测试 ===\n")

# 检查工作目录
if (!file.exists("global.R")) {
  stop("❌ 请在medical_analysis_system目录下运行此脚本")
}

# 加载系统
source("global.R")

# 1. 测试时间变量识别
cat("🕐 测试时间变量识别功能...\n")
test_datetime_detection <- function() {
  # 创建包含时间变量的测试数据
  test_data <- data.frame(
    icustay_id = 1:10,
    admission_time = c(
      "2125-02-01 05:34:00", "2125-02-02 10:15:30", "2125-02-03 14:22:45",
      "2125-02-04 08:45:12", "2125-02-05 16:30:00", "2125-02-06 12:00:00",
      "2125-02-07 09:15:30", "2125-02-08 18:45:00", "2125-02-09 07:30:15",
      "2125-02-10 20:00:00"
    ),
    discharge_date = c(
      "2125-02-05", "2125-02-08", "2125-02-10", "2125-02-12", "2125-02-15",
      "2125-02-18", "2125-02-20", "2125-02-22", "2125-02-25", "2125-02-28"
    ),
    age = sample(20:80, 10),
    stringsAsFactors = FALSE
  )
  
  cat("原始数据类型:\n")
  print(sapply(test_data, class))
  
  # 应用类型检测
  converted_data <- detect_and_convert_types(test_data)
  
  cat("\n转换后数据类型:\n")
  print(sapply(converted_data, class))
  
  # 检查时间变量是否正确识别
  if (inherits(converted_data$admission_time, "POSIXct")) {
    cat("✅ admission_time 正确识别为datetime类型\n")
  } else {
    cat("❌ admission_time 识别失败\n")
  }
  
  if (inherits(converted_data$discharge_date, "Date")) {
    cat("✅ discharge_date 正确识别为date类型\n")
  } else {
    cat("❌ discharge_date 识别失败\n")
  }
  
  return(converted_data)
}

# 2. 测试清洗报告生成
cat("\n🧹 测试清洗报告生成功能...\n")
test_cleaning_report <- function() {
  # 创建包含缺失值的测试数据
  original_data <- data.frame(
    icustay_id = 1:20,
    age = c(rep(NA, 5), sample(20:80, 15)),  # 25% 缺失
    gender = sample(c("M", "F", NA), 20, replace = TRUE),  # 少量缺失
    high_missing_var = c(rep(NA, 15), sample(1:5, 5)),  # 75% 缺失
    complete_var = sample(1:100, 20),  # 无缺失
    stringsAsFactors = FALSE
  )
  
  # 模拟清洗后数据（移除高缺失变量）
  cleaned_data <- original_data[, !names(original_data) %in% "high_missing_var"]
  
  # 生成清洗报告
  options <- list(
    missing_method = "mice",
    mice_iterations = 5,
    normalize = FALSE,
    remove_outliers = FALSE
  )
  
  report <- generate_cleaning_report(original_data, cleaned_data, options)
  
  cat("清洗报告内容:\n")
  cat("原始行数:", report$original_rows, "\n")
  cat("清洗后行数:", report$final_rows, "\n")
  cat("排除变量:", paste(report$excluded_variables, collapse = ", "), "\n")
  cat("排除变量缺失率:", report$missing_percentages[report$excluded_variables], "%\n")
  
  if (length(report$excluded_variables) > 0 && "high_missing_var" %in% report$excluded_variables) {
    cat("✅ 高缺失变量正确识别并排除\n")
  } else {
    cat("❌ 高缺失变量识别失败\n")
  }
  
  return(report)
}

# 3. 测试数据传递
cat("\n📊 测试数据传递功能...\n")
test_data_transfer <- function() {
  # 模拟values对象
  values <- reactiveValues(
    raw_data = NULL,
    processed_data = NULL
  )
  
  # 创建测试数据
  test_data <- data.frame(
    icustay_id = 1:10,
    age = sample(20:80, 10),
    gender = sample(c("M", "F"), 10, replace = TRUE),
    stringsAsFactors = FALSE
  )
  
  # 模拟数据传递
  values$raw_data <- test_data
  values$processed_data <- test_data
  
  if (!is.null(values$raw_data) && nrow(values$raw_data) == 10) {
    cat("✅ 数据成功传递到raw_data\n")
  } else {
    cat("❌ raw_data传递失败\n")
  }
  
  if (!is.null(values$processed_data) && nrow(values$processed_data) == 10) {
    cat("✅ 数据成功传递到processed_data\n")
  } else {
    cat("❌ processed_data传递失败\n")
  }
  
  return(values)
}

# 4. 生成增强测试数据
cat("\n📁 生成增强测试数据...\n")
generate_enhanced_test_data <- function() {
  # 创建包含时间变量的患者基本信息
  basic_info <- data.frame(
    icustay_id = 1:30,
    admission_time = seq(
      as.POSIXct("2125-01-01 00:00:00"), 
      by = "6 hours", 
      length.out = 30
    ),
    age = sample(18:90, 30, replace = TRUE),
    gender = sample(c("M", "F"), 30, replace = TRUE),
    admission_type = sample(c("Emergency", "Elective", "Urgent"), 30, replace = TRUE),
    stringsAsFactors = FALSE
  )
  
  # 转换时间为字符串（模拟CSV文件格式）
  basic_info$admission_time <- format(basic_info$admission_time, "%Y-%m-%d %H:%M:%S")
  
  # 创建实验室检查数据（包含高缺失变量）
  lab_data <- data.frame(
    icustay_id = sample(1:30, 25),
    hemoglobin = round(rnorm(25, 12, 2), 1),
    white_blood_cell = round(rnorm(25, 8000, 2000), 0),
    # 添加高缺失变量
    rare_test = c(rep(NA, 20), round(rnorm(5, 100, 20), 1)),
    creatinine = round(rnorm(25, 1.2, 0.5), 2),
    stringsAsFactors = FALSE
  )
  
  # 创建生命体征数据
  vital_signs <- data.frame(
    icustay_id = sample(1:30, 28),
    heart_rate = round(rnorm(28, 80, 15), 0),
    systolic_bp = round(rnorm(28, 120, 20), 0),
    temperature = round(rnorm(28, 36.5, 1), 1),
    stringsAsFactors = FALSE
  )
  
  # 创建结局数据
  outcomes <- data.frame(
    icustay_id = 1:30,
    discharge_time = seq(
      as.POSIXct("2125-01-05 00:00:00"), 
      by = "8 hours", 
      length.out = 30
    ),
    hospital_mortality = sample(c(0, 1), 30, replace = TRUE, prob = c(0.8, 0.2)),
    icu_length_of_stay = round(rnorm(30, 5, 3), 1),
    stringsAsFactors = FALSE
  )
  
  # 转换时间为字符串
  outcomes$discharge_time <- format(outcomes$discharge_time, "%Y-%m-%d %H:%M:%S")
  
  # 创建目录并保存文件
  if (!dir.exists("data/enhanced_test")) {
    dir.create("data/enhanced_test", recursive = TRUE)
  }
  
  # 保存不同格式的文件
  write.csv(basic_info, "data/enhanced_test/patient_basic_info.csv", row.names = FALSE)
  write.table(lab_data, "data/enhanced_test/laboratory_results.tsv", sep = "\t", row.names = FALSE, quote = FALSE)
  write.table(vital_signs, "data/enhanced_test/vital_signs_data.txt", sep = ";", row.names = FALSE, quote = FALSE)
  write.csv(outcomes, "data/enhanced_test/clinical_outcomes.csv", row.names = FALSE)
  
  cat("✅ 增强测试数据已生成到 data/enhanced_test/\n")
  cat("   📄 patient_basic_info.csv (包含时间变量)\n")
  cat("   📄 laboratory_results.tsv (包含高缺失变量)\n")
  cat("   📄 vital_signs_data.txt (分号分隔)\n")
  cat("   📄 clinical_outcomes.csv (包含时间变量)\n")
  
  return(list(
    basic_info = basic_info,
    lab_data = lab_data,
    vital_signs = vital_signs,
    outcomes = outcomes
  ))
}

# 运行所有测试
cat("\n开始运行增强功能测试...\n")

# 测试1: 时间变量识别
datetime_result <- test_datetime_detection()

# 测试2: 清洗报告生成
cleaning_result <- test_cleaning_report()

# 测试3: 数据传递
transfer_result <- test_data_transfer()

# 测试4: 生成测试数据
test_data_result <- generate_enhanced_test_data()

cat("\n=== 测试完成 ===\n")
cat("🎉 所有增强功能测试完成！\n")
cat("\n📖 功能说明:\n")
cat("1. ✅ 时间变量自动识别 - 支持多种时间格式\n")
cat("2. ✅ 清洗报告生成 - 详细显示排除的高缺失变量\n")
cat("3. ✅ 数据传递机制 - 确保数据可用于后续分析\n")
cat("4. ✅ 增强测试数据 - 包含真实的时间变量和缺失值模式\n")
cat("\n🚀 现在可以启动应用测试完整功能:\n")
cat("   source('simple_start.R')\n")

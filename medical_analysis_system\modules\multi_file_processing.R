# 医学数据分析系统 - 多文件处理模块
# Medical Data Analysis System - Multi-file Processing Module

# 多文件合并函数
merge_medical_files <- function(file_list, merge_key = "icustay_id", 
                                merge_type = "inner", remove_duplicates = TRUE,
                                add_source_info = TRUE) {
  tryCatch({
    if (length(file_list) < 2) {
      stop("至少需要2个文件进行合并")
    }
    
    log_info(paste("开始合并", length(file_list), "个文件"))
    
    # 检查所有文件是否都包含合并键
    missing_key_files <- c()
    for (i in seq_along(file_list)) {
      if (!merge_key %in% names(file_list[[i]]$data)) {
        missing_key_files <- c(missing_key_files, file_list[[i]]$name)
      }
    }
    
    if (length(missing_key_files) > 0) {
      stop(paste("以下文件缺少合并键", merge_key, ":", 
                paste(missing_key_files, collapse = ", ")))
    }
    
    # 开始合并过程
    merged_data <- file_list[[1]]$data
    merge_report <- list()
    merge_report$files_info <- list()
    
    # 记录第一个文件信息
    merge_report$files_info[[1]] <- list(
      name = file_list[[1]]$name,
      description = file_list[[1]]$description,
      rows = nrow(file_list[[1]]$data),
      cols = ncol(file_list[[1]]$data),
      variables = names(file_list[[1]]$data)
    )
    
    # 如果需要添加来源信息，为第一个文件添加来源列
    if (add_source_info) {
      source_col_name <- paste0("source_", gsub("[^A-Za-z0-9]", "_", file_list[[1]]$name))
      merged_data[[source_col_name]] <- 1
    }
    
    # 逐个合并其他文件
    for (i in 2:length(file_list)) {
      current_file <- file_list[[i]]
      current_data <- current_file$data
      
      log_info(paste("合并文件", i, ":", current_file$name))
      
      # 记录当前文件信息
      merge_report$files_info[[i]] <- list(
        name = current_file$name,
        description = current_file$description,
        rows = nrow(current_data),
        cols = ncol(current_data),
        variables = names(current_data)
      )
      
      # 如果需要添加来源信息
      if (add_source_info) {
        source_col_name <- paste0("source_", gsub("[^A-Za-z0-9]", "_", current_file$name))
        current_data[[source_col_name]] <- 1
      }
      
      # 检查重复列名（除了合并键）
      common_cols <- intersect(names(merged_data), names(current_data))
      duplicate_cols <- setdiff(common_cols, merge_key)
      
      if (length(duplicate_cols) > 0 && remove_duplicates) {
        log_warn(paste("发现重复列:", paste(duplicate_cols, collapse = ", "), 
                      "将从文件", current_file$name, "中移除"))
        current_data <- current_data[, !names(current_data) %in% duplicate_cols]
      } else if (length(duplicate_cols) > 0) {
        # 重命名重复列
        for (col in duplicate_cols) {
          new_name <- paste0(col, "_", gsub("[^A-Za-z0-9]", "_", current_file$name))
          names(current_data)[names(current_data) == col] <- new_name
          log_info(paste("重命名重复列:", col, "->", new_name))
        }
      }
      
      # 执行合并
      if (merge_type == "inner") {
        merged_data <- merge(merged_data, current_data, by = merge_key, all = FALSE)
      } else if (merge_type == "left") {
        merged_data <- merge(merged_data, current_data, by = merge_key, all.x = TRUE)
      } else if (merge_type == "outer") {
        merged_data <- merge(merged_data, current_data, by = merge_key, all = TRUE)
      }
      
      log_info(paste("合并后数据维度:", nrow(merged_data), "行,", ncol(merged_data), "列"))
    }
    
    # 生成合并报告
    merge_report$summary <- list(
      total_files = length(file_list),
      merge_key = merge_key,
      merge_type = merge_type,
      final_rows = nrow(merged_data),
      final_cols = ncol(merged_data),
      removed_duplicates = remove_duplicates,
      added_source_info = add_source_info
    )
    
    # 计算合并统计
    merge_report$statistics <- calculate_merge_statistics(file_list, merged_data, merge_key)
    
    log_info("文件合并完成")
    
    return(list(
      data = merged_data,
      report = merge_report
    ))
    
  }, error = function(e) {
    log_error(paste("文件合并失败:", e$message))
    stop(paste("文件合并失败:", e$message))
  })
}

# 计算合并统计信息
calculate_merge_statistics <- function(file_list, merged_data, merge_key) {
  tryCatch({
    stats <- list()
    
    # 计算每个文件的唯一ID数量
    unique_ids_by_file <- sapply(file_list, function(f) {
      length(unique(f$data[[merge_key]]))
    })
    
    # 计算合并后的唯一ID数量
    final_unique_ids <- length(unique(merged_data[[merge_key]]))
    
    stats$unique_ids <- list(
      by_file = unique_ids_by_file,
      final = final_unique_ids,
      total_original = sum(unique_ids_by_file)
    )
    
    # 计算数据覆盖率
    coverage <- final_unique_ids / max(unique_ids_by_file) * 100
    stats$coverage_rate <- round(coverage, 2)
    
    # 计算缺失值统计
    missing_stats <- sapply(merged_data, function(x) sum(is.na(x)))
    stats$missing_values <- list(
      total = sum(missing_stats),
      by_variable = missing_stats,
      percentage = round(sum(missing_stats) / (nrow(merged_data) * ncol(merged_data)) * 100, 2)
    )
    
    return(stats)
    
  }, error = function(e) {
    log_error(paste("统计计算失败:", e$message))
    return(list())
  })
}

# 验证文件兼容性
validate_file_compatibility <- function(file_list, merge_key = "icustay_id") {
  tryCatch({
    validation_results <- list()
    
    # 检查合并键存在性
    missing_key <- sapply(file_list, function(f) {
      !merge_key %in% names(f$data)
    })
    
    validation_results$missing_merge_key <- names(file_list)[missing_key]
    
    # 检查合并键数据类型一致性
    key_types <- sapply(file_list, function(f) {
      if (merge_key %in% names(f$data)) {
        class(f$data[[merge_key]])[1]
      } else {
        NA
      }
    })
    
    validation_results$key_type_consistency <- length(unique(key_types[!is.na(key_types)])) == 1
    validation_results$key_types <- key_types
    
    # 检查ID重叠情况
    all_ids <- lapply(file_list, function(f) {
      if (merge_key %in% names(f$data)) {
        unique(f$data[[merge_key]])
      } else {
        c()
      }
    })
    
    # 计算两两重叠
    overlap_matrix <- matrix(0, nrow = length(file_list), ncol = length(file_list))
    for (i in 1:length(file_list)) {
      for (j in 1:length(file_list)) {
        if (i != j) {
          overlap_matrix[i, j] <- length(intersect(all_ids[[i]], all_ids[[j]]))
        }
      }
    }
    
    validation_results$id_overlaps <- overlap_matrix
    validation_results$total_unique_ids <- length(unique(unlist(all_ids)))
    
    # 生成兼容性评分
    score <- 100
    if (length(validation_results$missing_merge_key) > 0) score <- score - 50
    if (!validation_results$key_type_consistency) score <- score - 30
    if (validation_results$total_unique_ids == 0) score <- 0
    
    validation_results$compatibility_score <- max(0, score)
    
    return(validation_results)
    
  }, error = function(e) {
    log_error(paste("文件兼容性验证失败:", e$message))
    return(list(compatibility_score = 0, error = e$message))
  })
}

# 生成合并预览
generate_merge_preview <- function(file_list, merge_key = "icustay_id", 
                                  merge_type = "inner", sample_size = 100) {
  tryCatch({
    if (length(file_list) < 2) {
      return(NULL)
    }
    
    # 对每个文件进行采样以加快预览速度
    sampled_files <- lapply(file_list, function(f) {
      if (nrow(f$data) > sample_size) {
        sample_indices <- sample(nrow(f$data), sample_size)
        f$data <- f$data[sample_indices, ]
      }
      return(f)
    })
    
    # 执行小规模合并
    preview_result <- merge_medical_files(
      sampled_files, 
      merge_key = merge_key,
      merge_type = merge_type,
      remove_duplicates = TRUE,
      add_source_info = FALSE
    )
    
    return(preview_result)
    
  }, error = function(e) {
    log_error(paste("合并预览生成失败:", e$message))
    return(NULL)
  })
}

# 导出合并数据
export_merged_data <- function(merged_data, filename = NULL, format = "csv") {
  tryCatch({
    if (is.null(filename)) {
      filename <- paste0("merged_medical_data_", Sys.Date(), ".", format)
    }
    
    if (format == "csv") {
      write.csv(merged_data, filename, row.names = FALSE, fileEncoding = "UTF-8")
    } else if (format == "xlsx") {
      library(openxlsx)
      write.xlsx(merged_data, filename)
    }
    
    log_info(paste("合并数据已导出:", filename))
    return(filename)
    
  }, error = function(e) {
    log_error(paste("数据导出失败:", e$message))
    stop(paste("数据导出失败:", e$message))
  })
}

# 测试多文件上传功能的简化启动脚本
# Simplified startup script for testing multi-file upload functionality

cat("=== 多文件上传功能测试 ===\n")

# 检查当前目录
if (!file.exists("global.R")) {
  stop("请在medical_analysis_system目录下运行此脚本")
}

# 生成测试数据
cat("📊 生成多种格式的测试数据...\n")
tryCatch({
  # 创建患者基本信息
  basic_info <- data.frame(
    icustay_id = 1:30,
    age = sample(18:90, 30, replace = TRUE),
    gender = sample(c("M", "F"), 30, replace = TRUE),
    admission_type = sample(c("Emergency", "Elective", "Urgent"), 30, replace = TRUE),
    bmi = round(rnorm(30, 25, 5), 1),
    stringsAsFactors = FALSE
  )

  # 创建实验室检查数据
  lab_data <- data.frame(
    icustay_id = sample(1:30, 25),  # 部分患者有实验室数据
    hemoglobin = round(rnorm(25, 12, 2), 1),
    white_blood_cell = round(rnorm(25, 8000, 2000), 0),
    platelet = round(rnorm(25, 250000, 50000), 0),
    creatinine = round(rnorm(25, 1.2, 0.5), 2),
    glucose = round(rnorm(25, 120, 30), 0),
    stringsAsFactors = FALSE
  )

  # 创建生命体征数据
  vital_signs <- data.frame(
    icustay_id = sample(1:30, 28),
    heart_rate = round(rnorm(28, 80, 15), 0),
    systolic_bp = round(rnorm(28, 120, 20), 0),
    diastolic_bp = round(rnorm(28, 80, 15), 0),
    temperature = round(rnorm(28, 36.5, 1), 1),
    respiratory_rate = round(rnorm(28, 18, 4), 0),
    stringsAsFactors = FALSE
  )

  # 创建临床结局数据
  outcomes <- data.frame(
    icustay_id = 1:30,
    hospital_mortality = sample(c(0, 1), 30, replace = TRUE, prob = c(0.8, 0.2)),
    icu_length_of_stay = round(rnorm(30, 5, 3), 1),
    hospital_length_of_stay = round(rnorm(30, 10, 5), 1),
    discharge_location = sample(c("Home", "SNF", "Rehab", "Death"), 30, replace = TRUE),
    stringsAsFactors = FALSE
  )

  # 创建目录并保存不同格式的文件
  if (!dir.exists("data/test")) {
    dir.create("data/test", recursive = TRUE)
  }

  # 保存CSV文件（逗号分隔）
  write.csv(basic_info, "data/test/basic_info.csv", row.names = FALSE)
  write.csv(lab_data, "data/test/lab_data.csv", row.names = FALSE)
  write.csv(vital_signs, "data/test/vital_signs.csv", row.names = FALSE)
  write.csv(outcomes, "data/test/outcomes.csv", row.names = FALSE)

  # 保存TSV文件（制表符分隔）
  write.table(basic_info, "data/test/basic_info.tsv", sep = "\t", row.names = FALSE, quote = FALSE)
  write.table(lab_data, "data/test/lab_data.tsv", sep = "\t", row.names = FALSE, quote = FALSE)

  # 保存分号分隔的文件
  write.table(vital_signs, "data/test/vital_signs_semicolon.txt", sep = ";", row.names = FALSE, quote = FALSE)

  cat("✅ 测试数据已生成:\n")
  cat("   CSV格式 (逗号分隔):\n")
  cat("     - data/test/basic_info.csv (30行, 5列)\n")
  cat("     - data/test/lab_data.csv (25行, 6列)\n")
  cat("     - data/test/vital_signs.csv (28行, 6列)\n")
  cat("     - data/test/outcomes.csv (30行, 5列)\n")
  cat("   TSV格式 (制表符分隔):\n")
  cat("     - data/test/basic_info.tsv\n")
  cat("     - data/test/lab_data.tsv\n")
  cat("   TXT格式 (分号分隔):\n")
  cat("     - data/test/vital_signs_semicolon.txt\n\n")

}, error = function(e) {
  cat("⚠️ 测试数据生成失败:", e$message, "\n")
})

# 启动应用
cat("🚀 启动应用...\n")
cat("应用启动后，请按以下步骤测试多文件上传功能:\n\n")
cat("📋 测试步骤:\n")
cat("1. 导航到: 数据管理 -> 多文件合并\n")
cat("2. 批量上传测试:\n")
cat("   - 点击'选择多个文件'按钮\n")
cat("   - 同时选择 data/test/ 目录下的多个文件\n")
cat("   - 或者拖拽多个文件到上传区域\n")
cat("   - 选择分隔符类型（可选择'自动检测'）\n")
cat("   - 点击'处理上传的文件'\n")
cat("3. 单个文件添加测试:\n")
cat("   - 点击'添加单个文件'按钮\n")
cat("   - 为每个文件选择不同的分隔符\n")
cat("4. 合并测试:\n")
cat("   - 配置合并选项（合并键、合并类型等）\n")
cat("   - 点击'开始合并'执行数据整合\n")
cat("   - 查看合并结果和质量报告\n")
cat("5. 下载合并数据\n\n")
cat("💡 功能特色:\n")
cat("   ✅ 支持拖拽多个文件上传\n")
cat("   ✅ 支持一次性选择多个文件\n")
cat("   ✅ 自动检测分隔符类型\n")
cat("   ✅ 自动生成文件描述\n")
cat("   ✅ 支持多种文件格式 (CSV, TSV, TXT)\n")
cat("   ✅ 智能合并和数据质量检查\n\n")

tryCatch({
  # 加载应用
  source("global.R")
  source("ui/ui_main.R")
  source("server/server_main.R")
  
  # 启动应用
  shiny::shinyApp(ui = ui, server = server)
  
}, error = function(e) {
  cat("❌ 应用启动失败:", e$message, "\n")
  cat("请检查依赖包是否完整安装\n")
})

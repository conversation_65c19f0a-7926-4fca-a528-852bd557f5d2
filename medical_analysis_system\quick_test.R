# 快速测试多文件合并功能
# Quick test for multi-file merge functionality

# 加载必要的模块
source("global.R")

# 创建简单测试数据
create_simple_test_data <- function() {
  # 基本信息表
  basic_info <- data.frame(
    icustay_id = 1:5,
    age = c(65, 72, 58, 81, 69),
    gender = c("M", "F", "M", "F", "M"),
    stringsAsFactors = FALSE
  )
  
  # 实验室检查表
  lab_data <- data.frame(
    icustay_id = c(1, 2, 3, 5),  # 缺少ID=4的数据
    hemoglobin = c(12.5, 10.8, 13.2, 11.9),
    creatinine = c(1.2, 2.1, 0.9, 1.5),
    stringsAsFactors = FALSE
  )
  
  # 结局表
  outcomes <- data.frame(
    icustay_id = 1:5,
    mortality = c(0, 1, 0, 1, 0),
    los_days = c(3, 7, 2, 12, 4),
    stringsAsFactors = FALSE
  )
  
  return(list(
    basic_info = basic_info,
    lab_data = lab_data,
    outcomes = outcomes
  ))
}

# 测试合并功能
test_merge <- function() {
  cat("开始测试多文件合并功能...\n")
  
  # 创建测试数据
  test_data <- create_simple_test_data()
  
  # 准备文件列表
  file_list <- list(
    list(
      name = "basic_info.csv",
      description = "基本信息",
      data = test_data$basic_info
    ),
    list(
      name = "lab_data.csv", 
      description = "实验室检查",
      data = test_data$lab_data
    ),
    list(
      name = "outcomes.csv",
      description = "临床结局",
      data = test_data$outcomes
    )
  )
  
  # 显示原始数据
  cat("\n=== 原始数据 ===\n")
  cat("基本信息表:\n")
  print(test_data$basic_info)
  cat("\n实验室检查表:\n")
  print(test_data$lab_data)
  cat("\n结局表:\n")
  print(test_data$outcomes)
  
  # 测试不同的合并类型
  merge_types <- c("inner", "left", "outer")
  
  for (merge_type in merge_types) {
    cat(paste("\n=== 测试", merge_type, "合并 ===\n"))
    
    tryCatch({
      result <- merge_medical_files(
        file_list = file_list,
        merge_key = "icustay_id",
        merge_type = merge_type,
        remove_duplicates = TRUE,
        add_source_info = FALSE
      )
      
      cat(paste("合并成功! 结果维度:", nrow(result$data), "行,", ncol(result$data), "列\n"))
      cat("合并结果:\n")
      print(result$data)
      
      # 显示合并统计
      report <- result$report
      cat(paste("数据覆盖率:", report$statistics$coverage_rate, "%\n"))
      
    }, error = function(e) {
      cat(paste("合并失败:", e$message, "\n"))
    })
  }
}

# 运行测试
cat("=== 多文件合并功能快速测试 ===\n")
test_merge()
cat("\n=== 测试完成 ===\n")

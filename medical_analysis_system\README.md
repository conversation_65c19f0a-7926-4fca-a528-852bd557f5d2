# 医学数据分析系统 (Medical Data Analysis System)

## 🏥 系统概述

这是一个基于Shiny的现代化Web界面医学数据分析系统，专门为医学研究人员和数据分析师设计。系统将您现有的复杂R代码重构为用户友好的图形界面，实现了从数据上传到报告生成的完整分析流程自动化。

## ✨ 核心特色

### 🔄 智能数据预处理
- **单文件上传**：支持CSV、Excel等多种格式
- **🆕 多文件合并**：通过icustay_id自动合并多个数据表
- **自动数据清洗**：智能识别和处理异常值
- **缺失值处理**：支持MICE多重插补、均值/中位数填充等多种方法
- **变量类型识别**：自动检测并转换数据类型
- **数据质量评估**：全面的数据质量检查和报告

### 📊 完整统计分析流程
- **描述性统计**：自动生成Table 1和基线特征表
- **单因素分析**：批量进行单因素Logistic回归分析
- **LASSO回归**：变量选择和正则化建模
- **多因素分析**：多变量Logistic回归建模
- **模型评估**：C-index、AUC、校准度等性能指标

### 📈 专业医学统计图表
- **森林图**：OR值和置信区间可视化
- **ROC曲线**：模型判别能力评估
- **校准曲线**：预测概率校准度评估
- **决策曲线分析(DCA)**：临床决策价值评估
- **列线图(Nomogram)**：预测模型可视化工具

### 🎯 预测模型构建
- **Logistic回归建模**：二分类结局预测
- **生存分析**：时间-事件分析(规划中)
- **模型验证**：交叉验证和外部验证
- **性能比较**：多模型性能对比分析

### 📋 自动化报告生成
- **多格式支持**：HTML、PDF、Word格式
- **专业模板**：标准、简洁、学术等多种模板
- **自定义内容**：灵活选择报告包含的分析内容
- **一键导出**：完整的分析报告自动生成

### 🌐 现代化Web界面
- **响应式设计**：支持桌面和平板设备
- **交互式图表**：可缩放、可导出的高质量图表
- **实时反馈**：分析进度和状态实时显示
- **用户友好**：直观的操作界面和清晰的错误提示

## 🚀 快速开始

### 方法一：运行演示版本
```r
# 1. 进入系统目录
setwd("medical_analysis_system")

# 2. 运行演示脚本
source("demo.R")
```

### 方法二：运行完整系统
```r
# 1. 安装依赖包
source("install_packages.R")

# 2. 启动完整系统
source("run_app.R")
```

### 方法三：直接启动
```r
# 在RStudio中运行
shiny::runApp("medical_analysis_system")
```

### 🆕 方法四：测试多文件合并功能
```r
# 生成测试数据并启动应用
source("medical_analysis_system/run_multi_file_test.R")
```

## 📁 项目架构

```
medical_analysis_system/
├── 🚀 启动文件
│   ├── app.R                    # Shiny应用主入口
│   ├── run_app.R               # 系统启动脚本
│   ├── demo.R                  # 演示版本
│   └── global.R                # 全局配置和包加载
│
├── 🎨 用户界面 (ui/)
│   ├── ui_main.R               # 主界面布局和导航
│   ├── ui_dashboard.R          # 仪表板和系统概览
│   ├── ui_data.R               # 数据上传、预览、清洗
│   ├── ui_analysis.R           # 统计分析配置界面
│   ├── ui_results.R            # 结果展示和交互图表
│   ├── ui_reports.R            # 报告生成和管理
│   └── ui_settings.R           # 系统设置和用户偏好
│
├── ⚙️ 服务器逻辑 (server/)
│   ├── server_main.R           # 主服务器逻辑和事件处理
│   ├── server_data.R           # 数据处理和验证逻辑
│   ├── server_analysis.R       # 分析执行和结果处理
│   ├── server_results.R        # 结果展示和图表生成
│   └── server_reports.R        # 报告生成和导出逻辑
│
├── 🧠 核心分析模块 (modules/)
│   ├── data_processing.R       # 数据预处理和清洗算法
│   ├── statistical_analysis.R # 统计分析和建模算法
│   ├── visualization.R         # 图表生成和可视化
│   └── model_building.R        # 预测模型构建和评估
│
├── 🛠️ 工具函数 (utils/)
│   ├── helpers.R               # 通用辅助函数
│   ├── validators.R            # 数据验证和错误检查
│   └── config.R                # 系统配置和参数管理
│
├── 🎨 静态资源 (www/)
│   ├── css/custom.css          # 自定义样式表
│   ├── js/                     # JavaScript增强功能
│   └── images/                 # 图标和图片资源
│
├── 📊 数据和输出
│   ├── data/                   # 示例数据和用户上传数据
│   ├── reports/                # 生成的分析报告
│   ├── exports/                # 导出的结果文件
│   ├── logs/                   # 系统运行日志
│   └── temp/                   # 临时文件存储
│
└── 📚 文档和配置
    ├── README.md               # 系统说明文档
    ├── install_packages.R      # 依赖包安装脚本
    └── config/                 # 配置文件目录
```

## 💻 系统要求

### 基础环境
- **R版本**: >= 4.0.0 (推荐 4.3.0+)
- **RStudio**: 推荐使用最新版本
- **内存**: >= 4GB (推荐 8GB+)
- **存储**: >= 1GB 可用空间
- **浏览器**: Chrome, Firefox, Safari, Edge (现代浏览器)

### 网络要求
- 首次安装需要网络连接下载R包
- 运行时可离线使用

## 📦 安装指南

### 1. 克隆或下载项目
```bash
# 如果使用Git
git clone https://github.com/your-repo/medical-analysis-system.git

# 或直接下载ZIP文件并解压
```

### 2. 安装R包依赖
```r
# 进入项目目录
setwd("medical_analysis_system")

# 自动安装所有依赖包
source("install_packages.R")
```

### 3. 验证安装
```r
# 运行系统检查
source("run_app.R")
```

## 🎯 使用指南

### 基础使用流程

#### 1️⃣ 数据准备
- 准备CSV、Excel等格式的医学数据
- 确保数据包含必要的变量和足够样本量
- 建议样本量 ≥ 30，变量数量适中

#### 2️⃣ 数据上传
**单文件上传**：
- 访问"数据管理" → "单文件上传"页面
- 拖拽或选择数据文件上传
- 配置文件读取参数（分隔符、编码等）

**🆕 多文件合并**：
- 访问"数据管理" → "多文件合并"页面
- 点击"添加文件"上传多个CSV文件
- 为每个文件添加描述信息（如：基本信息、实验室检查等）
- 选择合并选项：
  - 合并键：默认为icustay_id
  - 合并类型：内连接/左连接/外连接
  - 是否移除重复列
  - 是否添加数据来源标识
- 点击"开始合并"执行数据整合

#### 3️⃣ 数据预览和清洗
- 在"数据预览"页面检查数据质量
- 使用"数据清洗"功能处理缺失值和异常值
- 查看数据分布和相关性分析

#### 4️⃣ 统计分析
- **描述性统计**: 生成基线特征表
- **单因素分析**: 筛选候选变量
- **LASSO回归**: 自动变量选择
- **多因素分析**: 构建最终预测模型

#### 5️⃣ 结果查看
- 在"结果展示"页面查看分析结果
- 生成专业统计图表
- 评估模型性能指标

#### 6️⃣ 报告生成
- 在"报告中心"配置报告选项
- 选择报告模板和输出格式
- 一键生成完整分析报告

### 高级功能

#### 🔧 系统配置
- 在"系统设置"中自定义分析参数
- 调整文件大小限制、默认方法等
- 配置可视化和性能选项

#### 👤 用户偏好
- 个性化界面主题和语言
- 自定义分析偏好和输出格式
- 保存和恢复个人设置

#### 📊 交互式分析
- 使用交互式图表探索数据
- 实时调整参数查看结果变化
- 动态筛选和分组分析

## 🎨 界面预览

### 主要页面功能

| 页面 | 功能描述 | 主要特色 |
|------|----------|----------|
| 🏠 **仪表板** | 系统概览和快速开始 | 状态监控、活动历史 |
| 📊 **数据管理** | 数据上传、预览、清洗 | 拖拽上传、质量检查 |
| 📈 **统计分析** | 各种统计分析方法 | 参数配置、批量分析 |
| 🎯 **模型构建** | 预测模型构建和评估 | 自动建模、性能评估 |
| 📋 **结果展示** | 结果查看和可视化 | 交互图表、多格式导出 |
| 📄 **报告中心** | 报告生成和管理 | 模板选择、一键生成 |

## 🔧 技术架构

### 核心技术栈
- **后端框架**: R + Shiny
- **前端UI**: shinydashboard + Bootstrap
- **数据处理**: dplyr, tidyr, mice
- **统计分析**: rms, survival, glmnet, pROC
- **可视化**: ggplot2, plotly, forestplot
- **报告生成**: rmarkdown, knitr

### 设计特色
- **🏗️ 模块化架构**: 松耦合设计，易于维护和扩展
- **📱 响应式设计**: 自适应不同屏幕尺寸和设备
- **⚡ 异步处理**: 长时间分析不阻塞用户界面
- **🛡️ 错误处理**: 完善的错误捕获和用户友好提示
- **📝 日志系统**: 详细的操作和分析日志记录
- **🎨 现代UI**: 美观的界面设计和流畅的用户体验

## 🆚 与传统R脚本对比

| 特性 | 传统R脚本 | 本系统 |
|------|-----------|--------|
| **易用性** | 需要编程知识 | ✅ 图形界面，无需编程 |
| **数据上传** | 手动修改代码 | ✅ 拖拽上传，自动识别 |
| **参数配置** | 修改脚本参数 | ✅ 界面配置，实时预览 |
| **结果查看** | 控制台输出 | ✅ 交互式图表和表格 |
| **报告生成** | 手动整理 | ✅ 一键生成专业报告 |
| **错误处理** | 脚本中断 | ✅ 友好提示，继续操作 |
| **可重复性** | 依赖脚本版本 | ✅ 参数保存，结果可追溯 |
| **协作性** | 需要R环境 | ✅ 浏览器访问，易于分享 |

## 📚 示例数据

系统内置了医学研究常用的示例数据集：

### 🏥 MIMIC风格数据
- **患者基本信息**: 年龄、性别、BMI等
- **生命体征**: 心率、血压、呼吸频率等
- **实验室检查**: 肌酐、尿素氮、白细胞等
- **评分系统**: SOFA评分、APACHE评分等
- **合并症**: 糖尿病、高血压、心脏病等
- **结局指标**: 28天死亡率、ICU住院时长等

### 📊 数据特点
- **样本量**: 500例患者
- **变量数**: 18个变量
- **缺失值**: 约5%的缺失率
- **结局事件**: 约20%的事件发生率

### 🆕 多文件测试数据
运行测试脚本后，系统会在`data/test/`目录生成以下测试文件：

| 文件名 | 描述 | 主要变量 | 记录数 |
|--------|------|----------|--------|
| `basic_info.csv` | 患者基本信息 | icustay_id, age, gender, admission_type | 100 |
| `lab_data.csv` | 实验室检查 | icustay_id, hemoglobin, wbc, platelet, creatinine | ~80 |
| `vital_signs.csv` | 生命体征 | icustay_id, heart_rate, bp, temperature, rr | ~90 |
| `outcomes.csv` | 临床结局 | icustay_id, mortality, los_icu, los_hospital | 100 |

**特点**：
- 所有文件通过`icustay_id`关联
- 模拟真实MIMIC数据库的多表结构
- 包含部分缺失数据，适合测试合并功能

## 🚨 注意事项

### 数据安全
- 🔒 所有数据在本地处理，不上传到外部服务器
- 🗑️ 临时文件自动清理，保护数据隐私
- 📋 建议使用去标识化的数据进行分析

### 使用限制
- 📊 建议样本量 ≥ 30例
- 📁 单个文件大小 ≤ 100MB（可配置）
- 🔢 变量数量建议 ≤ 样本量的1/10
- ⏱️ 复杂分析可能需要较长时间

### 结果解读
- 📖 统计结果需要结合专业知识解读
- ⚠️ 注意多重比较和过拟合问题
- 🔍 建议进行外部验证确认结果

## 🤝 贡献指南

我们欢迎社区贡献！您可以通过以下方式参与：

### 🐛 报告问题
- 在GitHub Issues中报告bug
- 提供详细的错误信息和重现步骤
- 建议改进功能和用户体验

### 💡 功能建议
- 提出新功能需求
- 分享使用经验和最佳实践
- 参与功能设计讨论

### 🔧 代码贡献
- Fork项目并创建功能分支
- 遵循代码规范和注释要求
- 提交Pull Request并描述更改内容

## 📞 技术支持

### 📧 联系方式
- **邮箱**: <EMAIL>
- **GitHub**: https://github.com/your-repo/medical-analysis-system
- **文档**: https://docs.medical-analysis.com

### 🆘 常见问题
1. **Q**: 支持哪些数据格式？
   **A**: CSV, TSV, TXT, Excel (.xlsx, .xls)

2. **Q**: 如何处理大文件？
   **A**: 可在系统设置中调整文件大小限制，或分批处理数据

3. **Q**: 分析结果如何解读？
   **A**: 系统提供详细的统计指标，建议结合医学背景知识解读

4. **Q**: 如何保存分析结果？
   **A**: 可下载结果表格、图表，或生成完整的分析报告

## 📄 许可证

本项目采用 **MIT License** 开源协议。

```
MIT License

Copyright (c) 2024 Medical Data Analysis Team

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
```

---

## 🎉 致谢

感谢所有为医学数据分析做出贡献的研究人员和开发者！

**让医学数据分析变得更简单、更高效、更专业！** 🚀

# 医学数据分析系统 - 模型构建模块
# Medical Data Analysis System - Model Building Module

# 构建Logistic回归模型
build_logistic_model <- function(data, outcome_var, predictors, method = "enter") {
  tryCatch({
    log_info("开始构建Logistic回归模型")
    
    # 数据验证
    if (is.null(data) || nrow(data) == 0) {
      stop("数据为空")
    }
    
    if (!outcome_var %in% names(data)) {
      stop("结局变量不存在")
    }
    
    if (length(predictors) == 0) {
      stop("未指定预测变量")
    }
    
    # 检查预测变量是否存在
    missing_predictors <- predictors[!predictors %in% names(data)]
    if (length(missing_predictors) > 0) {
      stop(paste("以下预测变量不存在:", paste(missing_predictors, collapse = ", ")))
    }
    
    # 构建公式
    formula_str <- paste(outcome_var, "~", paste(predictors, collapse = " + "))
    model_formula <- as.formula(formula_str)
    
    # 拟合模型
    model <- glm(model_formula, data = data, family = binomial())
    
    # 模型摘要
    model_summary <- summary(model)
    
    # 计算OR和置信区间
    coef_table <- model_summary$coefficients
    if (nrow(coef_table) > 1) {
      # 排除截距
      coef_data <- coef_table[-1, , drop = FALSE]
      
      OR <- exp(coef_data[, 1])
      SE <- coef_data[, 2]
      CI_lower <- exp(coef_data[, 1] - 1.96 * SE)
      CI_upper <- exp(coef_data[, 1] + 1.96 * SE)
      p_values <- coef_data[, 4]
      
      # 构建结果表
      results_table <- data.frame(
        Variable = rownames(coef_data),
        Coefficient = round(coef_data[, 1], 4),
        SE = round(SE, 4),
        OR = round(OR, 4),
        CI_Lower = round(CI_lower, 4),
        CI_Upper = round(CI_upper, 4),
        CI = paste0(round(CI_lower, 4), "-", round(CI_upper, 4)),
        P_Value = round(p_values, 4),
        Significance = ifelse(p_values < 0.001, "***",
                             ifelse(p_values < 0.01, "**",
                                   ifelse(p_values < 0.05, "*", ""))),
        stringsAsFactors = FALSE
      )
    } else {
      results_table <- data.frame()
    }
    
    # 模型评估指标
    model_metrics <- list(
      AIC = AIC(model),
      BIC = BIC(model),
      deviance = model$deviance,
      null_deviance = model$null.deviance,
      df_residual = model$df.residual,
      df_null = model$df.null
    )
    
    # 返回结果
    model_results <- list(
      model = model,
      formula = formula_str,
      results_table = results_table,
      model_summary = model_summary,
      model_metrics = model_metrics,
      method = method
    )
    
    log_info("Logistic回归模型构建完成")
    return(model_results)
    
  }, error = function(e) {
    log_error(paste("Logistic回归模型构建失败:", e$message))
    stop(paste("Logistic回归模型构建失败:", e$message))
  })
}

# 构建生存分析模型
build_survival_model <- function(data, time_var, event_var, predictors) {
  tryCatch({
    log_info("开始构建生存分析模型")
    
    # 检查是否有survival包
    if (!requireNamespace("survival", quietly = TRUE)) {
      stop("需要安装survival包")
    }
    
    library(survival)
    
    # 数据验证
    if (!time_var %in% names(data)) {
      stop("时间变量不存在")
    }
    
    if (!event_var %in% names(data)) {
      stop("事件变量不存在")
    }
    
    # 构建生存对象
    surv_obj <- Surv(data[[time_var]], data[[event_var]])
    
    # 构建公式
    formula_str <- paste("surv_obj ~", paste(predictors, collapse = " + "))
    model_formula <- as.formula(formula_str)
    
    # 拟合Cox回归模型
    cox_model <- coxph(model_formula, data = data)
    
    # 模型摘要
    model_summary <- summary(cox_model)
    
    # 构建结果表
    coef_table <- model_summary$coefficients
    conf_int <- model_summary$conf.int
    
    results_table <- data.frame(
      Variable = rownames(coef_table),
      Coefficient = round(coef_table[, 1], 4),
      SE = round(coef_table[, 3], 4),
      HR = round(coef_table[, 2], 4),
      CI_Lower = round(conf_int[, 3], 4),
      CI_Upper = round(conf_int[, 4], 4),
      CI = paste0(round(conf_int[, 3], 4), "-", round(conf_int[, 4], 4)),
      P_Value = round(coef_table[, 5], 4),
      Significance = ifelse(coef_table[, 5] < 0.001, "***",
                           ifelse(coef_table[, 5] < 0.01, "**",
                                 ifelse(coef_table[, 5] < 0.05, "*", ""))),
      stringsAsFactors = FALSE
    )
    
    # 模型评估指标
    model_metrics <- list(
      concordance = model_summary$concordance[1],
      rsquare = model_summary$rsq[1],
      likelihood_ratio_test = model_summary$logtest[1],
      wald_test = model_summary$waldtest[1],
      score_test = model_summary$sctest[1]
    )
    
    # 返回结果
    survival_results <- list(
      model = cox_model,
      formula = formula_str,
      results_table = results_table,
      model_summary = model_summary,
      model_metrics = model_metrics,
      surv_object = surv_obj
    )
    
    log_info("生存分析模型构建完成")
    return(survival_results)
    
  }, error = function(e) {
    log_error(paste("生存分析模型构建失败:", e$message))
    stop(paste("生存分析模型构建失败:", e$message))
  })
}

# 构建列线图
build_nomogram <- function(model_results, title = "Nomogram") {
  tryCatch({
    log_info("开始构建列线图")
    
    # 检查是否有rms包
    if (!requireNamespace("rms", quietly = TRUE)) {
      stop("需要安装rms包")
    }
    
    library(rms)
    
    # 获取模型
    model <- model_results$model
    
    if (is.null(model)) {
      stop("模型对象为空")
    }
    
    # 根据模型类型构建列线图
    if (inherits(model, "glm")) {
      # Logistic回归列线图
      
      # 重新拟合模型使用rms包
      data <- model$data
      if (is.null(data)) {
        data <- eval(model$call$data)
      }
      
      # 使用lrm函数重新拟合
      formula_str <- model_results$formula
      lrm_model <- lrm(as.formula(formula_str), data = data)
      
      # 构建列线图
      nomogram_obj <- nomogram(
        lrm_model,
        fun = plogis,
        fun.at = c(0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9),
        funlabel = "Probability"
      )
      
    } else if (inherits(model, "coxph")) {
      # Cox回归列线图
      
      # 使用cph函数重新拟合
      data <- model$data
      if (is.null(data)) {
        data <- eval(model$call$data)
      }
      
      # 构建生存对象
      time_var <- all.vars(model$terms)[1]
      event_var <- all.vars(model$terms)[2]
      
      surv_obj <- Surv(data[[time_var]], data[[event_var]])
      predictors <- all.vars(model$terms)[-c(1, 2)]
      
      formula_str <- paste("surv_obj ~", paste(predictors, collapse = " + "))
      cph_model <- cph(as.formula(formula_str), data = data, surv = TRUE)
      
      # 构建列线图
      nomogram_obj <- nomogram(
        cph_model,
        fun = function(x) 1 - pexp(x, 1),
        funlabel = "Survival Probability"
      )
      
    } else {
      stop("不支持的模型类型")
    }
    
    # 返回结果
    nomogram_results <- list(
      nomogram = nomogram_obj,
      title = title,
      model_type = class(model)[1]
    )
    
    log_info("列线图构建完成")
    return(nomogram_results)
    
  }, error = function(e) {
    log_error(paste("列线图构建失败:", e$message))
    stop(paste("列线图构建失败:", e$message))
  })
}

# 模型交叉验证
cross_validate_model <- function(data, outcome_var, predictors, k_folds = 5, seed = 123) {
  tryCatch({
    log_info("开始模型交叉验证")
    
    set.seed(seed)
    
    # 创建折叠
    n <- nrow(data)
    fold_indices <- sample(rep(1:k_folds, length.out = n))
    
    # 存储结果
    cv_results <- list()
    auc_values <- numeric(k_folds)
    
    for (i in 1:k_folds) {
      # 训练集和测试集
      train_data <- data[fold_indices != i, ]
      test_data <- data[fold_indices == i, ]
      
      # 构建模型
      model_results <- build_logistic_model(train_data, outcome_var, predictors)
      model <- model_results$model
      
      # 预测
      pred_prob <- predict(model, newdata = test_data, type = "response")
      actual <- test_data[[outcome_var]]
      
      # 计算AUC
      if (requireNamespace("pROC", quietly = TRUE)) {
        library(pROC)
        roc_obj <- roc(actual, pred_prob, quiet = TRUE)
        auc_values[i] <- auc(roc_obj)
      } else {
        auc_values[i] <- NA
      }
      
      # 存储结果
      cv_results[[i]] <- list(
        fold = i,
        model = model,
        predictions = pred_prob,
        actual = actual,
        auc = auc_values[i]
      )
    }
    
    # 汇总结果
    cv_summary <- list(
      k_folds = k_folds,
      mean_auc = mean(auc_values, na.rm = TRUE),
      sd_auc = sd(auc_values, na.rm = TRUE),
      auc_values = auc_values,
      cv_results = cv_results
    )
    
    log_info("模型交叉验证完成")
    return(cv_summary)
    
  }, error = function(e) {
    log_error(paste("模型交叉验证失败:", e$message))
    stop(paste("模型交叉验证失败:", e$message))
  })
}

# 模型比较
compare_models <- function(model_list, data, outcome_var) {
  tryCatch({
    log_info("开始模型比较")
    
    if (length(model_list) < 2) {
      stop("至少需要两个模型进行比较")
    }
    
    # 存储比较结果
    comparison_results <- data.frame(
      Model = character(),
      AIC = numeric(),
      BIC = numeric(),
      AUC = numeric(),
      Accuracy = numeric(),
      Sensitivity = numeric(),
      Specificity = numeric(),
      stringsAsFactors = FALSE
    )
    
    for (i in seq_along(model_list)) {
      model_name <- names(model_list)[i]
      if (is.null(model_name)) model_name <- paste("Model", i)
      
      model <- model_list[[i]]$model
      
      # 基本指标
      aic_val <- AIC(model)
      bic_val <- BIC(model)
      
      # 预测性能
      pred_prob <- predict(model, type = "response")
      actual <- data[[outcome_var]]
      
      # AUC
      auc_val <- NA
      if (requireNamespace("pROC", quietly = TRUE)) {
        library(pROC)
        roc_obj <- roc(actual, pred_prob, quiet = TRUE)
        auc_val <- auc(roc_obj)
      }
      
      # 分类性能（使用0.5作为阈值）
      pred_class <- ifelse(pred_prob > 0.5, 1, 0)
      
      # 混淆矩阵
      confusion_matrix <- table(Predicted = pred_class, Actual = actual)
      
      if (nrow(confusion_matrix) == 2 && ncol(confusion_matrix) == 2) {
        TP <- confusion_matrix[2, 2]
        TN <- confusion_matrix[1, 1]
        FP <- confusion_matrix[2, 1]
        FN <- confusion_matrix[1, 2]
        
        accuracy <- (TP + TN) / sum(confusion_matrix)
        sensitivity <- TP / (TP + FN)
        specificity <- TN / (TN + FP)
      } else {
        accuracy <- sensitivity <- specificity <- NA
      }
      
      # 添加到结果
      comparison_results <- rbind(comparison_results, data.frame(
        Model = model_name,
        AIC = round(aic_val, 2),
        BIC = round(bic_val, 2),
        AUC = round(auc_val, 4),
        Accuracy = round(accuracy, 4),
        Sensitivity = round(sensitivity, 4),
        Specificity = round(specificity, 4),
        stringsAsFactors = FALSE
      ))
    }
    
    # 按AUC排序
    comparison_results <- comparison_results[order(comparison_results$AUC, decreasing = TRUE), ]
    rownames(comparison_results) <- NULL
    
    log_info("模型比较完成")
    return(comparison_results)
    
  }, error = function(e) {
    log_error(paste("模型比较失败:", e$message))
    stop(paste("模型比较失败:", e$message))
  })
}

# 测试多文件上传功能的简化启动脚本
# Simplified startup script for testing multi-file upload functionality

cat("=== 多文件上传功能测试 ===\n")

# 检查当前目录
if (!file.exists("global.R")) {
  stop("请在medical_analysis_system目录下运行此脚本")
}

# 生成测试数据
cat("📊 生成测试数据...\n")
tryCatch({
  # 创建简单测试数据
  basic_info <- data.frame(
    icustay_id = 1:20,
    age = sample(18:90, 20, replace = TRUE),
    gender = sample(c("M", "F"), 20, replace = TRUE),
    stringsAsFactors = FALSE
  )
  
  lab_data <- data.frame(
    icustay_id = sample(1:20, 15),  # 部分患者有实验室数据
    hemoglobin = round(rnorm(15, 12, 2), 1),
    creatinine = round(rnorm(15, 1.2, 0.5), 2),
    stringsAsFactors = FALSE
  )
  
  outcomes <- data.frame(
    icustay_id = 1:20,
    mortality = sample(c(0, 1), 20, replace = TRUE, prob = c(0.8, 0.2)),
    los_days = round(rnorm(20, 5, 3), 1),
    stringsAsFactors = FALSE
  )
  
  # 创建目录并保存文件
  if (!dir.exists("data/test")) {
    dir.create("data/test", recursive = TRUE)
  }
  
  write.csv(basic_info, "data/test/basic_info.csv", row.names = FALSE)
  write.csv(lab_data, "data/test/lab_data.csv", row.names = FALSE)
  write.csv(outcomes, "data/test/outcomes.csv", row.names = FALSE)
  
  cat("✅ 测试数据已生成:\n")
  cat("   - data/test/basic_info.csv (20行)\n")
  cat("   - data/test/lab_data.csv (15行)\n")
  cat("   - data/test/outcomes.csv (20行)\n\n")
  
}, error = function(e) {
  cat("⚠️ 测试数据生成失败:", e$message, "\n")
})

# 启动应用
cat("🚀 启动应用...\n")
cat("请导航到: 数据管理 -> 多文件合并\n")
cat("使用 data/test/ 目录下的CSV文件进行测试\n\n")

tryCatch({
  # 加载应用
  source("global.R")
  source("ui/ui_main.R")
  source("server/server_main.R")
  
  # 启动应用
  shiny::shinyApp(ui = ui, server = server)
  
}, error = function(e) {
  cat("❌ 应用启动失败:", e$message, "\n")
  cat("请检查依赖包是否完整安装\n")
})

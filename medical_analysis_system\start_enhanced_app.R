# 启动增强版多文件上传应用
# Start Enhanced Multi-file Upload Application

cat("=== 增强版多文件上传应用启动 ===\n")
cat("Enhanced Multi-file Upload Application Startup\n\n")

# 检查当前目录
if (!file.exists("global.R")) {
  stop("❌ 请在medical_analysis_system目录下运行此脚本")
}

cat("✅ 工作目录检查通过\n")

# 快速测试合并功能
cat("🔧 快速测试合并功能...\n")
tryCatch({
  # 创建简单测试数据
  df1 <- data.frame(icustay_id = 1:3, age = c(65, 72, 58))
  df2 <- data.frame(icustay_id = c(1, 3), score = c(80, 90))
  
  # 测试基本合并
  result <- merge(df1, df2, by = "icustay_id", all.x = TRUE)
  
  if (nrow(result) == 3 && ncol(result) == 3) {
    cat("✅ 合并功能测试通过\n")
  } else {
    cat("⚠️ 合并功能可能有问题\n")
  }
  
}, error = function(e) {
  cat("❌ 合并功能测试失败:", e$message, "\n")
})

# 生成增强版测试数据
cat("📊 生成增强版测试数据...\n")
tryCatch({
  # 创建患者基本信息（CSV格式，逗号分隔）
  basic_info <- data.frame(
    icustay_id = 1:25,
    age = sample(18:90, 25, replace = TRUE),
    gender = sample(c("M", "F"), 25, replace = TRUE),
    admission_type = sample(c("Emergency", "Elective", "Urgent"), 25, replace = TRUE),
    bmi = round(rnorm(25, 25, 5), 1),
    stringsAsFactors = FALSE
  )
  
  # 创建实验室检查（TSV格式，制表符分隔）
  lab_data <- data.frame(
    icustay_id = sample(1:25, 20),
    hemoglobin = round(rnorm(20, 12, 2), 1),
    white_blood_cell = round(rnorm(20, 8000, 2000), 0),
    platelet = round(rnorm(20, 250000, 50000), 0),
    creatinine = round(rnorm(20, 1.2, 0.5), 2),
    glucose = round(rnorm(20, 120, 30), 0),
    stringsAsFactors = FALSE
  )
  
  # 创建生命体征（分号分隔）
  vital_signs <- data.frame(
    icustay_id = sample(1:25, 22),
    heart_rate = round(rnorm(22, 80, 15), 0),
    systolic_bp = round(rnorm(22, 120, 20), 0),
    diastolic_bp = round(rnorm(22, 80, 15), 0),
    temperature = round(rnorm(22, 36.5, 1), 1),
    respiratory_rate = round(rnorm(22, 18, 4), 0),
    stringsAsFactors = FALSE
  )
  
  # 创建临床结局
  outcomes <- data.frame(
    icustay_id = 1:25,
    hospital_mortality = sample(c(0, 1), 25, replace = TRUE, prob = c(0.8, 0.2)),
    icu_length_of_stay = round(rnorm(25, 5, 3), 1),
    hospital_length_of_stay = round(rnorm(25, 10, 5), 1),
    discharge_location = sample(c("Home", "SNF", "Rehab", "Death"), 25, replace = TRUE),
    stringsAsFactors = FALSE
  )
  
  # 创建目录
  if (!dir.exists("data/enhanced_test")) {
    dir.create("data/enhanced_test", recursive = TRUE)
  }
  
  # 保存不同格式的文件
  write.csv(basic_info, "data/enhanced_test/patient_basic_info.csv", row.names = FALSE)
  write.table(lab_data, "data/enhanced_test/laboratory_results.tsv", sep = "\t", row.names = FALSE, quote = FALSE)
  write.table(vital_signs, "data/enhanced_test/vital_signs_data.txt", sep = ";", row.names = FALSE, quote = FALSE)
  write.csv(outcomes, "data/enhanced_test/clinical_outcomes.csv", row.names = FALSE)
  
  # 额外创建一些特殊格式文件用于测试
  write.table(basic_info[1:10, 1:3], "data/enhanced_test/demo_pipe_separated.txt", sep = "|", row.names = FALSE, quote = FALSE)
  write.table(lab_data[1:8, 1:4], "data/enhanced_test/demo_space_separated.txt", sep = " ", row.names = FALSE, quote = FALSE)
  
  cat("✅ 增强版测试数据已生成:\n")
  cat("   📁 data/enhanced_test/ 目录包含:\n")
  cat("   📄 patient_basic_info.csv (25行, 逗号分隔)\n")
  cat("   📄 laboratory_results.tsv (20行, 制表符分隔)\n")
  cat("   📄 vital_signs_data.txt (22行, 分号分隔)\n")
  cat("   📄 clinical_outcomes.csv (25行, 逗号分隔)\n")
  cat("   📄 demo_pipe_separated.txt (10行, 竖线分隔)\n")
  cat("   📄 demo_space_separated.txt (8行, 空格分隔)\n\n")
  
}, error = function(e) {
  cat("⚠️ 测试数据生成失败:", e$message, "\n")
})

# 启动应用
cat("🚀 启动增强版多文件上传应用...\n\n")
cat("📋 新功能特色:\n")
cat("   ✨ 拖拽多个文件上传\n")
cat("   ✨ 一次性选择多个文件\n")
cat("   ✨ 自动检测分隔符类型\n")
cat("   ✨ 智能生成文件描述\n")
cat("   ✨ 支持多种文件格式\n")
cat("   ✨ 增强的数据质量检查\n\n")

cat("📖 使用指南:\n")
cat("1. 导航到: 数据管理 → 多文件合并\n")
cat("2. 批量上传: 拖拽或选择 data/enhanced_test/ 目录下的多个文件\n")
cat("3. 分隔符选择: 尝试'自动检测'功能\n")
cat("4. 单个添加: 使用'添加单个文件'按钮逐个配置\n")
cat("5. 合并数据: 配置合并选项并执行合并\n")
cat("6. 查看结果: 浏览合并报告和数据质量分析\n\n")

tryCatch({
  # 加载应用
  source("global.R")
  source("ui/ui_main.R")
  source("server/server_main.R")
  
  cat("🎉 应用启动成功！\n")
  cat("请在浏览器中测试新的多文件上传功能。\n\n")
  
  # 启动应用
  shiny::shinyApp(ui = ui, server = server)
  
}, error = function(e) {
  cat("❌ 应用启动失败:", e$message, "\n")
  cat("请检查以下事项:\n")
  cat("1. 所有依赖包是否已安装\n")
  cat("2. 是否在正确的目录下运行\n")
  cat("3. 文件权限是否正确\n")
  cat("\n可以尝试运行: source('install_packages.R')\n")
})

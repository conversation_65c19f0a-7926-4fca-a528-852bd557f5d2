# 医学数据分析系统 - 主程序测试脚本
# Medical Data Analysis System - Main Program Test Script

cat("=================================================\n")
cat("测试主程序组件\n")
cat("Testing Main Program Components\n")
cat("=================================================\n\n")

# 检查工作目录
if (!file.exists("app.R")) {
  if (file.exists("medical_analysis_system/app.R")) {
    setwd("medical_analysis_system")
  }
}

cat("当前工作目录:", getwd(), "\n\n")

# 测试函数
test_component <- function(file_path, description) {
  cat("测试:", description, "\n")
  
  if (!file.exists(file_path)) {
    cat("  ✗ 文件不存在:", file_path, "\n")
    return(FALSE)
  }
  
  tryCatch({
    source(file_path, local = TRUE)
    cat("  ✓ 加载成功\n")
    return(TRUE)
  }, error = function(e) {
    cat("  ✗ 加载失败:", e$message, "\n")
    return(FALSE)
  })
}

# 1. 测试全局配置
cat("1. 测试全局配置\n")
global_ok <- test_component("global.R", "全局配置")

# 2. 测试工具函数
cat("\n2. 测试工具函数\n")
helpers_ok <- test_component("utils/helpers.R", "辅助函数")
validators_ok <- test_component("utils/validators.R", "数据验证")
config_ok <- test_component("utils/config.R", "配置管理")

# 3. 测试核心模块
cat("\n3. 测试核心模块\n")
data_proc_ok <- test_component("modules/data_processing.R", "数据处理模块")
stat_analysis_ok <- test_component("modules/statistical_analysis.R", "统计分析模块")
visualization_ok <- test_component("modules/visualization.R", "可视化模块")
model_building_ok <- test_component("modules/model_building.R", "模型构建模块")

# 4. 测试UI模块
cat("\n4. 测试UI模块\n")
ui_dashboard_ok <- test_component("ui/ui_dashboard.R", "仪表板界面")
ui_data_ok <- test_component("ui/ui_data.R", "数据管理界面")
ui_analysis_ok <- test_component("ui/ui_analysis.R", "分析配置界面")
ui_results_ok <- test_component("ui/ui_results.R", "结果展示界面")
ui_reports_ok <- test_component("ui/ui_reports.R", "报告中心界面")
ui_settings_ok <- test_component("ui/ui_settings.R", "系统设置界面")

# 5. 测试主UI
cat("\n5. 测试主UI\n")
ui_main_ok <- test_component("ui/ui_main.R", "主界面")

# 6. 测试服务器逻辑
cat("\n6. 测试服务器逻辑\n")
server_ok <- test_component("server/server_main.R", "主服务器逻辑")

# 汇总结果
cat("\n=================================================\n")
cat("测试结果汇总\n")
cat("Test Results Summary\n")
cat("=================================================\n")

components <- list(
  "全局配置" = global_ok,
  "辅助函数" = helpers_ok,
  "数据验证" = validators_ok,
  "配置管理" = config_ok,
  "数据处理模块" = data_proc_ok,
  "统计分析模块" = stat_analysis_ok,
  "可视化模块" = visualization_ok,
  "模型构建模块" = model_building_ok,
  "仪表板界面" = ui_dashboard_ok,
  "数据管理界面" = ui_data_ok,
  "分析配置界面" = ui_analysis_ok,
  "结果展示界面" = ui_results_ok,
  "报告中心界面" = ui_reports_ok,
  "系统设置界面" = ui_settings_ok,
  "主界面" = ui_main_ok,
  "主服务器逻辑" = server_ok
)

success_count <- 0
total_count <- length(components)

for (name in names(components)) {
  status <- if (components[[name]]) "✓" else "✗"
  cat(sprintf("%-20s %s\n", name, status))
  if (components[[name]]) success_count <- success_count + 1
}

cat("\n")
cat("成功:", success_count, "/", total_count, "\n")
cat("Success:", success_count, "/", total_count, "\n")

if (success_count == total_count) {
  cat("\n🎉 所有组件测试通过！可以启动主程序。\n")
  cat("🎉 All components passed! Ready to start main application.\n")

  cat("\n现在您可以使用以下命令启动完整版系统:\n")
  cat("Now you can start the full system with:\n")
  cat("source('run_main_app.R')\n")
  cat("或者直接运行: source('app.R')\n")
  cat("Or run directly: source('app.R')\n")

} else {
  cat("\n⚠️ 部分组件测试失败。建议使用简化版本。\n")
  cat("⚠️ Some components failed. Recommend using simplified version.\n")
  cat("运行: source('quick_start.R')\n")
  cat("Run: source('quick_start.R')\n")
}

cat("\n=================================================\n")

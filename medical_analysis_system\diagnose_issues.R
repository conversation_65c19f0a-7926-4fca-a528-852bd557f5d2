# 问题诊断脚本
# Issue Diagnosis Script

cat("=== 多文件上传功能问题诊断 ===\n\n")

# 检查工作目录
cat("1. 检查工作目录...\n")
cat("当前目录:", getwd(), "\n")
if (file.exists("global.R")) {
  cat("✅ 在正确的目录中\n\n")
} else {
  cat("❌ 不在正确的目录中\n\n")
  stop("请在medical_analysis_system目录下运行此脚本")
}

# 检查必需的包
cat("2. 检查必需的包...\n")
required_packages <- c("shiny", "shinydashboard", "shinyjs", "DT", "dplyr", "ggplot2")

for (pkg in required_packages) {
  if (requireNamespace(pkg, quietly = TRUE)) {
    cat("✅", pkg, "- 已安装\n")
  } else {
    cat("❌", pkg, "- 未安装\n")
  }
}
cat("\n")

# 检查shinyjs功能
cat("3. 检查shinyjs功能...\n")
tryCatch({
  library(shinyjs)
  cat("✅ shinyjs加载成功\n")
  
  # 检查是否有useShinyjs函数
  if (exists("useShinyjs")) {
    cat("✅ useShinyjs函数存在\n")
  } else {
    cat("❌ useShinyjs函数不存在\n")
  }
  
  # 检查是否有reset函数
  if (exists("reset", where = asNamespace("shinyjs"))) {
    cat("✅ shinyjs::reset函数存在\n")
  } else {
    cat("❌ shinyjs::reset函数不存在\n")
  }
  
}, error = function(e) {
  cat("❌ shinyjs加载失败:", e$message, "\n")
})
cat("\n")

# 检查文件结构
cat("4. 检查文件结构...\n")
required_files <- c(
  "global.R",
  "ui/ui_main.R",
  "server/server_main.R",
  "server/server_multi_file.R",
  "modules/multi_file_processing.R"
)

for (file in required_files) {
  if (file.exists(file)) {
    cat("✅", file, "\n")
  } else {
    cat("❌", file, "- 缺失\n")
  }
}
cat("\n")

# 测试基本的merge函数
cat("5. 测试基本merge函数...\n")
tryCatch({
  df1 <- data.frame(id = 1:3, x = c("a", "b", "c"))
  df2 <- data.frame(id = c(1, 3), y = c(10, 30))
  
  result <- merge(df1, df2, by = "id", all.x = TRUE)
  
  if (nrow(result) == 3 && ncol(result) == 3) {
    cat("✅ 基本merge函数正常工作\n")
  } else {
    cat("❌ merge函数结果异常\n")
    print(result)
  }
  
}, error = function(e) {
  cat("❌ merge函数测试失败:", e$message, "\n")
})
cat("\n")

# 测试文件读取
cat("6. 测试文件读取功能...\n")
tryCatch({
  # 创建临时测试文件
  temp_data <- data.frame(
    icustay_id = 1:5,
    age = c(65, 72, 58, 81, 69),
    gender = c("M", "F", "M", "F", "M")
  )
  
  temp_file <- tempfile(fileext = ".csv")
  write.csv(temp_data, temp_file, row.names = FALSE)
  
  # 测试读取
  read_data <- read.csv(temp_file, stringsAsFactors = FALSE)
  
  if (nrow(read_data) == 5 && ncol(read_data) == 3) {
    cat("✅ 文件读取功能正常\n")
  } else {
    cat("❌ 文件读取结果异常\n")
  }
  
  # 清理临时文件
  unlink(temp_file)
  
}, error = function(e) {
  cat("❌ 文件读取测试失败:", e$message, "\n")
})
cat("\n")

# 测试模块加载
cat("7. 测试模块加载...\n")
tryCatch({
  source("global.R")
  cat("✅ global.R加载成功\n")
  
  # 检查是否定义了必要的函数
  if (exists("detect_separator")) {
    cat("✅ detect_separator函数已定义\n")
  } else {
    cat("❌ detect_separator函数未定义\n")
  }
  
  if (exists("generate_auto_description")) {
    cat("✅ generate_auto_description函数已定义\n")
  } else {
    cat("❌ generate_auto_description函数未定义\n")
  }
  
  if (exists("merge_medical_files")) {
    cat("✅ merge_medical_files函数已定义\n")
  } else {
    cat("❌ merge_medical_files函数未定义\n")
  }
  
}, error = function(e) {
  cat("❌ 模块加载失败:", e$message, "\n")
})
cat("\n")

# 生成诊断报告
cat("=== 诊断报告 ===\n")
cat("如果上述检查中有❌标记，请按以下步骤解决:\n\n")

cat("📦 包安装问题:\n")
cat("   install.packages(c('shiny', 'shinydashboard', 'shinyjs', 'DT', 'dplyr', 'ggplot2'))\n\n")

cat("📁 文件缺失问题:\n")
cat("   确保所有必需文件都存在于正确位置\n\n")

cat("🔧 shinyjs问题:\n")
cat("   尝试重新安装: install.packages('shinyjs', dependencies = TRUE)\n")
cat("   或使用开发版: devtools::install_github('daattali/shinyjs')\n\n")

cat("🚀 如果所有检查都通过，可以尝试启动应用:\n")
cat("   source('simple_start.R')\n\n")

cat("诊断完成！\n")

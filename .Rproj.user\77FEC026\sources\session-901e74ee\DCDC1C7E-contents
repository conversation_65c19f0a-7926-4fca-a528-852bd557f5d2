# 测试多文件上传和合并功能
# Test Multi-file Upload and Merge Functionality

# 加载必要的包
library(shiny)
library(shinydashboard)
library(DT)

# 设置工作目录
#setwd("medical_analysis_system")

# 加载全局配置和模块
source("global.R")

# 创建测试数据
create_test_data <- function() {
  # 创建基本信息表
  basic_info <- data.frame(
    icustay_id = 1:100,
    age = sample(18:90, 100, replace = TRUE),
    gender = sample(c("M", "F"), 100, replace = TRUE),
    admission_type = sample(c("Emergency", "Elective", "Urgent"), 100, replace = TRUE),
    stringsAsFactors = FALSE
  )
  
  # 创建实验室检查表
  lab_data <- data.frame(
    icustay_id = sample(1:100, 80),  # 部分患者有实验室数据
    hemoglobin = round(rnorm(80, 12, 2), 1),
    white_blood_cell = round(rnorm(80, 8000, 2000), 0),
    platelet = round(rnorm(80, 250000, 50000), 0),
    creatinine = round(rnorm(80, 1.2, 0.5), 2),
    stringsAsFactors = FALSE
  )
  
  # 创建生命体征表
  vital_signs <- data.frame(
    icustay_id = sample(1:100, 90),  # 部分患者有生命体征数据
    heart_rate = round(rnorm(90, 80, 15), 0),
    systolic_bp = round(rnorm(90, 120, 20), 0),
    diastolic_bp = round(rnorm(90, 80, 15), 0),
    temperature = round(rnorm(90, 36.5, 1), 1),
    respiratory_rate = round(rnorm(90, 18, 4), 0),
    stringsAsFactors = FALSE
  )
  
  # 创建结局表
  outcomes <- data.frame(
    icustay_id = 1:100,
    hospital_mortality = sample(c(0, 1), 100, replace = TRUE, prob = c(0.8, 0.2)),
    icu_length_of_stay = round(rnorm(100, 5, 3), 1),
    hospital_length_of_stay = round(rnorm(100, 10, 5), 1),
    stringsAsFactors = FALSE
  )
  
  return(list(
    basic_info = basic_info,
    lab_data = lab_data,
    vital_signs = vital_signs,
    outcomes = outcomes
  ))
}

# 测试合并功能
test_merge_function <- function() {
  cat("开始测试多文件合并功能...\n")
  
  # 创建测试数据
  test_data <- create_test_data()
  
  # 准备文件列表
  file_list <- list(
    list(
      name = "basic_info.csv",
      description = "基本信息",
      data = test_data$basic_info
    ),
    list(
      name = "lab_data.csv", 
      description = "实验室检查",
      data = test_data$lab_data
    ),
    list(
      name = "vital_signs.csv",
      description = "生命体征", 
      data = test_data$vital_signs
    ),
    list(
      name = "outcomes.csv",
      description = "临床结局",
      data = test_data$outcomes
    )
  )
  
  # 测试不同的合并类型
  merge_types <- c("inner", "left", "outer")
  
  for (merge_type in merge_types) {
    cat(paste("\n测试", merge_type, "合并...\n"))
    
    tryCatch({
      result <- merge_medical_files(
        file_list = file_list,
        merge_key = "icustay_id",
        merge_type = merge_type,
        remove_duplicates = TRUE,
        add_source_info = TRUE
      )
      
      cat(paste("合并成功! 结果维度:", nrow(result$data), "行,", ncol(result$data), "列\n"))
      cat(paste("唯一患者数:", length(unique(result$data$icustay_id)), "\n"))
      
      # 显示合并报告摘要
      report <- result$report
      cat("合并统计:\n")
      cat(paste("  - 文件数:", report$summary$total_files, "\n"))
      cat(paste("  - 最终行数:", report$summary$final_rows, "\n"))
      cat(paste("  - 最终列数:", report$summary$final_cols, "\n"))
      cat(paste("  - 缺失值比例:", report$statistics$missing_values$percentage, "%\n"))
      cat(paste("  - 数据覆盖率:", report$statistics$coverage_rate, "%\n"))
      
    }, error = function(e) {
      cat(paste("合并失败:", e$message, "\n"))
    })
  }
}

# 测试文件兼容性验证
test_compatibility_validation <- function() {
  cat("\n开始测试文件兼容性验证...\n")
  
  test_data <- create_test_data()
  
  # 创建兼容的文件列表
  compatible_files <- list(
    list(name = "file1.csv", data = test_data$basic_info),
    list(name = "file2.csv", data = test_data$lab_data)
  )
  
  # 创建不兼容的文件列表（缺少合并键）
  incompatible_data <- test_data$lab_data
  names(incompatible_data)[1] <- "patient_id"  # 改变合并键名称
  
  incompatible_files <- list(
    list(name = "file1.csv", data = test_data$basic_info),
    list(name = "file2.csv", data = incompatible_data)
  )
  
  # 测试兼容文件
  cat("测试兼容文件...\n")
  result1 <- validate_file_compatibility(compatible_files, "icustay_id")
  cat(paste("兼容性评分:", result1$compatibility_score, "\n"))
  
  # 测试不兼容文件
  cat("测试不兼容文件...\n")
  result2 <- validate_file_compatibility(incompatible_files, "icustay_id")
  cat(paste("兼容性评分:", result2$compatibility_score, "\n"))
  cat(paste("缺少合并键的文件:", paste(result2$missing_merge_key, collapse = ", "), "\n"))
}

# 保存测试数据到CSV文件
save_test_data <- function() {
  cat("保存测试数据到CSV文件...\n")
  
  test_data <- create_test_data()
  
  # 创建data目录
  if (!dir.exists("data/test")) {
    dir.create("data/test", recursive = TRUE)
  }
  
  # 保存各个文件
  write.csv(test_data$basic_info, "data/test/basic_info.csv", row.names = FALSE)
  write.csv(test_data$lab_data, "data/test/lab_data.csv", row.names = FALSE)
  write.csv(test_data$vital_signs, "data/test/vital_signs.csv", row.names = FALSE)
  write.csv(test_data$outcomes, "data/test/outcomes.csv", row.names = FALSE)
  
  cat("测试数据已保存到 data/test/ 目录\n")
  cat("文件列表:\n")
  cat("  - basic_info.csv (基本信息)\n")
  cat("  - lab_data.csv (实验室检查)\n") 
  cat("  - vital_signs.csv (生命体征)\n")
  cat("  - outcomes.csv (临床结局)\n")
}

# 运行所有测试
run_all_tests <- function() {
  cat("=== 多文件上传和合并功能测试 ===\n")
  
  # 测试合并功能
  test_merge_function()
  
  # 测试兼容性验证
  test_compatibility_validation()
  
  # 保存测试数据
  save_test_data()
  
  cat("\n=== 测试完成 ===\n")
  cat("您可以使用保存的测试数据来测试Shiny应用的多文件上传功能\n")
}

# 如果直接运行此脚本，执行所有测试
if (interactive()) {
  run_all_tests()
} else {
  cat("测试脚本已加载。运行 run_all_tests() 来执行所有测试。\n")
}

# 运行多文件上传功能测试
# Run Multi-file Upload Functionality Test

# 设置工作目录
if (basename(getwd()) != "medical_analysis_system") {
  if (dir.exists("medical_analysis_system")) {
    setwd("medical_analysis_system")
  } else {
    stop("请在包含medical_analysis_system目录的路径下运行此脚本")
  }
}

cat("当前工作目录:", getwd(), "\n")

# 检查必要文件是否存在
required_files <- c(
  "global.R",
  "modules/multi_file_processing.R",
  "modules/data_processing.R",
  "ui/ui_main.R",
  "server/server_main.R"
)

missing_files <- c()
for (file in required_files) {
  if (!file.exists(file)) {
    missing_files <- c(missing_files, file)
  }
}

if (length(missing_files) > 0) {
  cat("缺少以下必要文件:\n")
  for (file in missing_files) {
    cat("  -", file, "\n")
  }
  stop("请确保所有必要文件都存在")
}

# 加载测试脚本
source("test_multi_file.R")

# 运行测试
cat("\n开始运行多文件功能测试...\n")
run_all_tests()

# 启动Shiny应用进行交互测试
cat("\n准备启动Shiny应用进行交互测试...\n")
cat("应用启动后，请导航到 '数据管理' -> '多文件合并' 页面\n")
cat("您可以使用 data/test/ 目录下的测试文件进行测试\n")

# 询问是否启动应用
if (interactive()) {
  response <- readline("是否启动Shiny应用? (y/n): ")
  if (tolower(response) %in% c("y", "yes", "是")) {
    cat("启动Shiny应用...\n")
    
    # 加载应用
    source("ui/ui_main.R")
    source("server/server_main.R")
    
    # 运行应用
    shinyApp(ui = ui, server = server)
  } else {
    cat("测试完成。您可以稍后手动启动应用。\n")
  }
} else {
  cat("在非交互模式下运行。测试完成。\n")
}

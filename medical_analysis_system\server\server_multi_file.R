# 医学数据分析系统 - 多文件上传服务器逻辑
# Medical Data Analysis System - Multi-file Upload Server Logic

# 多文件上传服务器逻辑
server_multi_file <- function(input, output, session, multi_file_values, values = NULL) {
  
  # 添加新文件输入
  observeEvent(input$add_file_input, {
    multi_file_values$file_counter <- multi_file_values$file_counter + 1
    counter <- multi_file_values$file_counter
    
    # 动态创建新的文件输入UI
    insertUI(
      selector = "#file_inputs_container",
      where = "beforeEnd",
      ui = div(
        id = paste0("file_input_", counter),
        class = "file-input-group",
        style = "border: 1px solid #ddd; border-radius: 8px; padding: 15px; margin-bottom: 15px; background: #f8f9fa;",
        
        fluidRow(
          column(8,
            fileInput(
              paste0("multi_file_", counter),
              paste("文件", counter, ":"),
              accept = c(".csv", ".tsv", ".txt"),
              width = "100%"
            )
          ),
          column(3,
            textInput(
              paste0("file_desc_", counter),
              "文件描述:",
              placeholder = "例如：实验室检查"
            )
          ),
          column(1,
            br(),
            actionButton(
              paste0("remove_file_", counter),
              "",
              icon = icon("times"),
              class = "btn-danger btn-sm",
              style = "margin-top: 5px;",
              onclick = paste0("Shiny.setInputValue('remove_file_id', ", counter, ", {priority: 'event'});")
            )
          )
        )
      )
    )
    
    show_success(paste("已添加文件输入", counter))
  })
  
  # 移除文件输入
  observeEvent(input$remove_file_id, {
    removeUI(selector = paste0("#file_input_", input$remove_file_id))
    
    # 从存储中移除对应文件
    file_key <- paste0("multi_file_", input$remove_file_id)
    if (file_key %in% names(multi_file_values$files)) {
      multi_file_values$files[[file_key]] <- NULL
    }
    
    show_success("文件输入已移除")
  })
  
  # 清空所有文件
  observeEvent(input$clear_all_files, {
    # 移除所有动态创建的文件输入（保留第一个）
    for (i in 2:multi_file_values$file_counter) {
      removeUI(selector = paste0("#file_input_", i))
    }
    
    # 重置计数器和数据
    multi_file_values$file_counter <- 1
    multi_file_values$files <- list()
    multi_file_values$merged_data <- NULL
    multi_file_values$merge_report <- NULL
    
    # 重置第一个文件输入
    updateFileInput(session, "multi_file_1", value = NULL)
    updateTextInput(session, "file_desc_1", value = "")
    
    show_success("所有文件已清空")
  })
  
  # 监听文件上传
  observe({
    for (i in 1:multi_file_values$file_counter) {
      local({
        file_id <- paste0("multi_file_", i)
        desc_id <- paste0("file_desc_", i)
        
        file_input <- input[[file_id]]
        file_desc <- input[[desc_id]]
        
        if (!is.null(file_input)) {
          tryCatch({
            # 读取文件
            data <- read_medical_data(
              file_input$datapath,
              separator = ",",
              encoding = "UTF-8",
              header = TRUE,
              stringsAsFactors = FALSE
            )
            
            # 存储文件信息
            multi_file_values$files[[file_id]] <- list(
              name = file_input$name,
              description = if (is.null(file_desc) || file_desc == "") paste("文件", i) else file_desc,
              data = data,
              upload_time = Sys.time()
            )
            
            show_success(paste("文件", file_input$name, "上传成功"))
            
          }, error = function(e) {
            show_warning(paste("文件", file_input$name, "上传失败:", e$message))
          })
        }
      })
    }
  })
  
  # 文件状态表格输出（响应式）
  output$file_status_table <- DT::renderDataTable({
    if (length(multi_file_values$files) == 0) {
      data.frame(
        文件名 = "暂无文件",
        描述 = "",
        行数 = "",
        列数 = "",
        状态 = "等待上传",
        stringsAsFactors = FALSE
      )
    } else {
      status_data <- do.call(rbind, lapply(names(multi_file_values$files), function(key) {
        file_info <- multi_file_values$files[[key]]
        data.frame(
          文件名 = file_info$name,
          描述 = file_info$description,
          行数 = nrow(file_info$data),
          列数 = ncol(file_info$data),
          状态 = "已上传",
          stringsAsFactors = FALSE
        )
      }))
      status_data
    }
  }, options = list(dom = 't', pageLength = 10))
  
  # 文件合并处理
  observeEvent(input$merge_files, {
    req(length(multi_file_values$files) >= 2)
    
    tryCatch({
      showNotification("正在合并文件...", type = "message", duration = NULL, id = "merge_msg")
      
      # 准备文件列表
      file_list <- lapply(multi_file_values$files, function(f) {
        list(
          name = f$name,
          description = f$description,
          data = f$data
        )
      })
      
      # 执行合并
      merge_result <- merge_medical_files(
        file_list = file_list,
        merge_key = input$merge_key,
        merge_type = input$merge_type,
        remove_duplicates = input$remove_duplicates,
        add_source_info = input$add_source_info
      )
      
      # 存储合并结果
      multi_file_values$merged_data <- merge_result$data
      multi_file_values$merge_report <- merge_result$report

      # 更新主数据（用于后续分析）
      if (!is.null(values)) {
        values$raw_data <- merge_result$data
        values$processed_data <- NULL  # 重置处理后的数据
      }

      removeNotification("merge_msg")
      show_success(paste("文件合并成功！共", nrow(merge_result$data), "行,",
                        ncol(merge_result$data), "列"))
      
      # 显示合并结果区域
      shinyjs::show("merge_results_section")
      
      # 更新合并预览信息
      output$merged_rows <- renderText(nrow(merge_result$data))
      output$merged_cols <- renderText(ncol(merge_result$data))
      output$merged_files <- renderText(length(file_list))
      output$used_merge_key <- renderText(input$merge_key)
      
      shinyjs::show("merge_preview_content")
      shinyjs::hide("merge_preview_empty")
      
    }, error = function(e) {
      removeNotification("merge_msg")
      show_warning(paste("文件合并失败:", e$message))
    })
  })
  
  # 合并数据预览表格
  output$merged_data_preview <- DT::renderDataTable({
    req(multi_file_values$merged_data)
    multi_file_values$merged_data
  }, options = list(
    scrollX = TRUE,
    pageLength = 10,
    dom = 'Bfrtip',
    buttons = c('copy', 'csv', 'excel')
  ))
  
  # 下载合并数据
  output$download_merged_data <- downloadHandler(
    filename = function() {
      paste0("merged_medical_data_", Sys.Date(), ".csv")
    },
    content = function(file) {
      req(multi_file_values$merged_data)
      write.csv(multi_file_values$merged_data, file, row.names = FALSE, fileEncoding = "UTF-8")
    }
  )
}

# 医学数据分析系统 - 多文件上传服务器逻辑
# Medical Data Analysis System - Multi-file Upload Server Logic

# 多文件上传服务器逻辑
server_multi_file <- function(input, output, session, multi_file_values, values = NULL) {

  # 批量文件上传处理
  observeEvent(input$multi_files_batch, {
    req(input$multi_files_batch)

    tryCatch({
      showNotification("正在处理上传的文件...", type = "message", duration = NULL, id = "batch_upload_msg")

      files_info <- input$multi_files_batch

      # 清空现有文件
      multi_file_values$files <- list()
      multi_file_values$file_counter <- 0

      # 处理每个上传的文件
      for (i in 1:nrow(files_info)) {
        file_info <- files_info[i, ]

        tryCatch({
          # 检测分隔符
          separator <- detect_separator(file_info$datapath, input$batch_separator)

          # 读取文件
          data <- read_medical_data(
            file_info$datapath,
            separator = separator,
            encoding = input$batch_encoding,
            header = input$batch_header,
            stringsAsFactors = FALSE
          )

          # 生成文件描述
          file_desc <- if (input$batch_auto_desc) {
            generate_auto_description(file_info$name, data)
          } else {
            paste("文件", i)
          }

          # 存储文件信息
          file_key <- paste0("batch_file_", i)
          multi_file_values$files[[file_key]] <- list(
            name = file_info$name,
            description = file_desc,
            data = data,
            upload_time = Sys.time(),
            separator = separator,
            encoding = input$batch_encoding
          )

          multi_file_values$file_counter <- multi_file_values$file_counter + 1

        }, error = function(e) {
          show_warning(paste("文件", file_info$name, "处理失败:", e$message))
        })
      }

      removeNotification("batch_upload_msg")
      show_success(paste("成功处理", length(multi_file_values$files), "个文件"))

    }, error = function(e) {
      removeNotification("batch_upload_msg")
      show_warning(paste("批量文件处理失败:", e$message))
    })
  })

  # 添加单个文件输入
  observeEvent(input$add_single_file, {
    multi_file_values$file_counter <- multi_file_values$file_counter + 1
    counter <- multi_file_values$file_counter

    # 动态创建新的文件输入UI
    insertUI(
      selector = "#single_file_inputs_container",
      where = "beforeEnd",
      ui = div(
        id = paste0("single_file_input_", counter),
        class = "single-file-input-group",
        style = "border: 1px solid #ddd; border-radius: 8px; padding: 15px; margin-bottom: 15px; background: #f8f9fa;",

        fluidRow(
          column(6,
            fileInput(
              paste0("single_file_", counter),
              paste("单个文件", counter, ":"),
              accept = c(".csv", ".tsv", ".txt"),
              width = "100%"
            )
          ),
          column(3,
            textInput(
              paste0("single_desc_", counter),
              "文件描述:",
              placeholder = "例如：实验室检查"
            )
          ),
          column(2,
            selectInput(
              paste0("single_sep_", counter),
              "分隔符:",
              choices = list(
                "自动" = "auto",
                "逗号" = ",",
                "制表符" = "\t",
                "分号" = ";",
                "空格" = " "
              ),
              selected = "auto"
            )
          ),
          column(1,
            br(),
            actionButton(
              paste0("remove_single_", counter),
              "",
              icon = icon("times"),
              class = "btn-danger btn-sm",
              style = "margin-top: 5px;",
              onclick = paste0("Shiny.setInputValue('remove_single_id', ", counter, ", {priority: 'event'});")
            )
          )
        )
      )
    )

    show_success(paste("已添加单个文件输入", counter))
  })
  
  # 移除单个文件输入
  observeEvent(input$remove_single_id, {
    removeUI(selector = paste0("#single_file_input_", input$remove_single_id))

    # 从存储中移除对应文件
    file_key <- paste0("single_file_", input$remove_single_id)
    if (file_key %in% names(multi_file_values$files)) {
      multi_file_values$files[[file_key]] <- NULL
    }

    show_success("单个文件输入已移除")
  })
  
  # 清空所有文件
  observeEvent(input$clear_all_files, {
    # 移除所有动态创建的单个文件输入
    for (i in 1:multi_file_values$file_counter) {
      removeUI(selector = paste0("#single_file_input_", i))
    }

    # 重置计数器和数据
    multi_file_values$file_counter <- 0
    multi_file_values$files <- list()
    multi_file_values$merged_data <- NULL
    multi_file_values$merge_report <- NULL

    # 重置批量文件输入
    updateFileInput(session, "multi_files_batch", value = NULL)

    show_success("所有文件已清空")
  })
  
  # 监听单个文件上传
  observe({
    for (i in 1:multi_file_values$file_counter) {
      local({
        file_id <- paste0("single_file_", i)
        desc_id <- paste0("single_desc_", i)
        sep_id <- paste0("single_sep_", i)

        file_input <- input[[file_id]]
        file_desc <- input[[desc_id]]
        file_sep <- input[[sep_id]]

        if (!is.null(file_input)) {
          tryCatch({
            # 检测分隔符
            separator <- detect_separator(file_input$datapath, file_sep)

            # 读取文件
            data <- read_medical_data(
              file_input$datapath,
              separator = separator,
              encoding = input$batch_encoding,
              header = input$batch_header,
              stringsAsFactors = FALSE
            )

            # 存储文件信息
            multi_file_values$files[[file_id]] <- list(
              name = file_input$name,
              description = if (is.null(file_desc) || file_desc == "") paste("文件", i) else file_desc,
              data = data,
              upload_time = Sys.time(),
              separator = separator,
              encoding = input$batch_encoding
            )

            show_success(paste("文件", file_input$name, "上传成功"))

          }, error = function(e) {
            show_warning(paste("文件", file_input$name, "上传失败:", e$message))
          })
        }
      })
    }
  })
  
  # 文件状态表格输出（响应式）
  output$file_status_table <- DT::renderDataTable({
    if (length(multi_file_values$files) == 0) {
      data.frame(
        文件名 = "暂无文件",
        描述 = "",
        行数 = "",
        列数 = "",
        状态 = "等待上传",
        stringsAsFactors = FALSE
      )
    } else {
      status_data <- do.call(rbind, lapply(names(multi_file_values$files), function(key) {
        file_info <- multi_file_values$files[[key]]
        data.frame(
          文件名 = file_info$name,
          描述 = file_info$description,
          行数 = nrow(file_info$data),
          列数 = ncol(file_info$data),
          状态 = "已上传",
          stringsAsFactors = FALSE
        )
      }))
      status_data
    }
  }, options = list(dom = 't', pageLength = 10))
  
  # 文件合并处理
  observeEvent(input$merge_files, {
    req(length(multi_file_values$files) >= 2)
    
    tryCatch({
      showNotification("正在合并文件...", type = "message", duration = NULL, id = "merge_msg")
      
      # 准备文件列表
      file_list <- lapply(multi_file_values$files, function(f) {
        list(
          name = f$name,
          description = f$description,
          data = f$data
        )
      })
      
      # 执行合并
      merge_result <- merge_medical_files(
        file_list = file_list,
        merge_key = input$merge_key,
        merge_type = input$merge_type,
        remove_duplicates = input$remove_duplicates,
        add_source_info = input$add_source_info
      )
      
      # 存储合并结果
      multi_file_values$merged_data <- merge_result$data
      multi_file_values$merge_report <- merge_result$report

      # 更新主数据（用于后续分析）
      if (!is.null(values)) {
        values$raw_data <- merge_result$data
        values$processed_data <- NULL  # 重置处理后的数据
      }

      removeNotification("merge_msg")
      show_success(paste("文件合并成功！共", nrow(merge_result$data), "行,",
                        ncol(merge_result$data), "列"))
      
      # 显示合并结果区域
      shinyjs::show("merge_results_section")
      
      # 更新合并预览信息
      output$merged_rows <- renderText(nrow(merge_result$data))
      output$merged_cols <- renderText(ncol(merge_result$data))
      output$merged_files <- renderText(length(file_list))
      output$used_merge_key <- renderText(input$merge_key)
      
      shinyjs::show("merge_preview_content")
      shinyjs::hide("merge_preview_empty")
      
    }, error = function(e) {
      removeNotification("merge_msg")
      show_warning(paste("文件合并失败:", e$message))
    })
  })
  
  # 合并数据预览表格
  output$merged_data_preview <- DT::renderDataTable({
    req(multi_file_values$merged_data)
    multi_file_values$merged_data
  }, options = list(
    scrollX = TRUE,
    pageLength = 10,
    dom = 'Bfrtip',
    buttons = c('copy', 'csv', 'excel')
  ))
  
  # 合并报告内容
  output$merge_report_content <- renderUI({
    req(multi_file_values$merge_report)

    report <- multi_file_values$merge_report

    # 生成报告HTML
    tags$div(
      # 基本统计
      tags$div(
        class = "row",
        tags$div(
          class = "col-md-6",
          tags$div(
            style = "background: #d4edda; padding: 15px; border-radius: 8px; margin-bottom: 15px;",
            tags$h5("合并统计", style = "margin: 0 0 10px 0; color: #155724;"),
            tags$p(paste("合并文件数:", report$summary$total_files)),
            tags$p(paste("合并类型:", report$summary$merge_type)),
            tags$p(paste("最终行数:", report$summary$final_rows)),
            tags$p(paste("最终列数:", report$summary$final_cols))
          )
        ),
        tags$div(
          class = "col-md-6",
          tags$div(
            style = "background: #cce5ff; padding: 15px; border-radius: 8px; margin-bottom: 15px;",
            tags$h5("数据质量", style = "margin: 0 0 10px 0; color: #004085;"),
            tags$p(paste("缺失值总数:", report$statistics$missing_values$total)),
            tags$p(paste("缺失值比例:", report$statistics$missing_values$percentage, "%")),
            tags$p(paste("数据覆盖率:", report$statistics$coverage_rate, "%"))
          )
        )
      ),

      # 文件详情
      tags$h5("文件详情", style = "color: #2c3e50; margin: 20px 0 15px 0;"),
      tags$div(
        do.call(tags$div, lapply(report$files_info, function(file_info) {
          tags$div(
            style = "border: 1px solid #ddd; border-radius: 5px; padding: 10px; margin-bottom: 10px;",
            tags$strong(file_info$name),
            tags$span(paste(" - ", file_info$description), style = "color: #666;"),
            tags$br(),
            tags$small(paste("行数:", file_info$rows, "| 列数:", file_info$cols))
          )
        }))
      )
    )
  })

  # 合并数据质量图表
  output$merged_data_quality <- renderPlot({
    req(multi_file_values$merged_data)

    data <- multi_file_values$merged_data

    # 创建缺失值分析图
    library(ggplot2)

    # 计算缺失值比例
    missing_data <- data.frame(
      Variable = names(data),
      Missing_Percent = sapply(data, function(x) sum(is.na(x)) / length(x) * 100),
      stringsAsFactors = FALSE
    )

    # 只显示有缺失值的变量
    missing_data <- missing_data[missing_data$Missing_Percent > 0, ]

    if (nrow(missing_data) == 0) {
      # 如果没有缺失值，显示数据完整性信息
      ggplot() +
        annotate("text", x = 0.5, y = 0.5,
                label = "数据完整，无缺失值",
                size = 20, color = "#2ecc71") +
        theme_void() +
        labs(title = "数据质量分析") +
        theme(plot.title = element_text(hjust = 0.5, size = 16, face = "bold"))
    } else {
      # 创建缺失值图表
      ggplot(missing_data, aes(x = reorder(Variable, Missing_Percent), y = Missing_Percent)) +
        geom_bar(stat = "identity", fill = "#3498db", alpha = 0.7) +
        coord_flip() +
        labs(
          title = "合并数据缺失值分析",
          x = "变量",
          y = "缺失值比例 (%)"
        ) +
        theme_minimal() +
        theme(
          plot.title = element_text(hjust = 0.5, size = 14, face = "bold"),
          axis.text = element_text(size = 10),
          axis.title = element_text(size = 12)
        )
    }
  })

  # 合并报告内容
  output$merge_report_content <- renderUI({
    req(multi_file_values$merge_report)

    report <- multi_file_values$merge_report

    # 生成报告HTML
    tags$div(
      # 基本统计
      tags$div(
        class = "row",
        tags$div(
          class = "col-md-6",
          tags$div(
            style = "background: #d4edda; padding: 15px; border-radius: 8px; margin-bottom: 15px;",
            tags$h5("合并统计", style = "margin: 0 0 10px 0; color: #155724;"),
            tags$p(paste("合并文件数:", report$summary$total_files)),
            tags$p(paste("合并类型:", report$summary$merge_type)),
            tags$p(paste("最终行数:", report$summary$final_rows)),
            tags$p(paste("最终列数:", report$summary$final_cols))
          )
        ),
        tags$div(
          class = "col-md-6",
          tags$div(
            style = "background: #cce5ff; padding: 15px; border-radius: 8px; margin-bottom: 15px;",
            tags$h5("数据质量", style = "margin: 0 0 10px 0; color: #004085;"),
            tags$p(paste("缺失值总数:", report$statistics$missing_values$total)),
            tags$p(paste("缺失值比例:", report$statistics$missing_values$percentage, "%")),
            tags$p(paste("数据覆盖率:", report$statistics$coverage_rate, "%"))
          )
        )
      ),

      # 文件详情
      tags$h5("文件详情", style = "color: #2c3e50; margin: 20px 0 15px 0;"),
      tags$div(
        do.call(tags$div, lapply(report$files_info, function(file_info) {
          tags$div(
            style = "border: 1px solid #ddd; border-radius: 5px; padding: 10px; margin-bottom: 10px;",
            tags$strong(file_info$name),
            tags$span(paste(" - ", file_info$description), style = "color: #666;"),
            tags$br(),
            tags$small(paste("行数:", file_info$rows, "| 列数:", file_info$cols))
          )
        }))
      )
    )
  })

  # 合并数据质量图表
  output$merged_data_quality <- renderPlot({
    req(multi_file_values$merged_data)

    data <- multi_file_values$merged_data

    # 创建缺失值分析图
    library(ggplot2)

    # 计算缺失值比例
    missing_data <- data.frame(
      Variable = names(data),
      Missing_Percent = sapply(data, function(x) sum(is.na(x)) / length(x) * 100),
      stringsAsFactors = FALSE
    )

    # 只显示有缺失值的变量
    missing_data <- missing_data[missing_data$Missing_Percent > 0, ]

    if (nrow(missing_data) == 0) {
      # 如果没有缺失值，显示数据完整性信息
      ggplot() +
        annotate("text", x = 0.5, y = 0.5,
                label = "数据完整，无缺失值",
                size = 20, color = "#2ecc71") +
        theme_void() +
        labs(title = "数据质量分析") +
        theme(plot.title = element_text(hjust = 0.5, size = 16, face = "bold"))
    } else {
      # 创建缺失值图表
      ggplot(missing_data, aes(x = reorder(Variable, Missing_Percent), y = Missing_Percent)) +
        geom_bar(stat = "identity", fill = "#3498db", alpha = 0.7) +
        coord_flip() +
        labs(
          title = "合并数据缺失值分析",
          x = "变量",
          y = "缺失值比例 (%)"
        ) +
        theme_minimal() +
        theme(
          plot.title = element_text(hjust = 0.5, size = 14, face = "bold"),
          axis.text = element_text(size = 10),
          axis.title = element_text(size = 12)
        )
    }
  })

  # 下载合并数据
  output$download_merged_data <- downloadHandler(
    filename = function() {
      paste0("merged_medical_data_", Sys.Date(), ".csv")
    },
    content = function(file) {
      req(multi_file_values$merged_data)
      write.csv(multi_file_values$merged_data, file, row.names = FALSE, fileEncoding = "UTF-8")
    }
  )
}

# 简化启动脚本 - 避免潜在问题
# Simplified startup script - avoiding potential issues

cat("=== 简化版多文件上传应用启动 ===\n")

# 检查目录
if (!file.exists("global.R")) {
  stop("❌ 请在medical_analysis_system目录下运行此脚本")
}

cat("✅ 目录检查通过\n")

# 生成简单测试数据
cat("📊 生成测试数据...\n")
tryCatch({
  # 创建基本信息
  basic_info <- data.frame(
    icustay_id = 1:20,
    age = sample(20:80, 20),
    gender = sample(c("M", "F"), 20, replace = TRUE),
    stringsAsFactors = FALSE
  )
  
  # 创建实验室数据
  lab_data <- data.frame(
    icustay_id = sample(1:20, 15),
    hemoglobin = round(rnorm(15, 12, 2), 1),
    creatinine = round(rnorm(15, 1.2, 0.5), 2),
    stringsAsFactors = FALSE
  )
  
  # 创建结局数据
  outcomes <- data.frame(
    icustay_id = 1:20,
    mortality = sample(c(0, 1), 20, replace = TRUE),
    los_days = round(rnorm(20, 5, 2), 1),
    stringsAsFactors = FALSE
  )
  
  # 创建目录
  if (!dir.exists("data/simple_test")) {
    dir.create("data/simple_test", recursive = TRUE)
  }
  
  # 保存文件
  write.csv(basic_info, "data/simple_test/basic_info.csv", row.names = FALSE)
  write.csv(lab_data, "data/simple_test/lab_data.csv", row.names = FALSE)
  write.csv(outcomes, "data/simple_test/outcomes.csv", row.names = FALSE)
  
  # 创建不同分隔符的文件
  write.table(basic_info, "data/simple_test/basic_info.tsv", sep = "\t", row.names = FALSE, quote = FALSE)
  write.table(lab_data, "data/simple_test/lab_data_semicolon.txt", sep = ";", row.names = FALSE, quote = FALSE)
  
  cat("✅ 测试数据已生成到 data/simple_test/\n")
  cat("   - basic_info.csv (逗号分隔)\n")
  cat("   - lab_data.csv (逗号分隔)\n")
  cat("   - outcomes.csv (逗号分隔)\n")
  cat("   - basic_info.tsv (制表符分隔)\n")
  cat("   - lab_data_semicolon.txt (分号分隔)\n\n")
  
}, error = function(e) {
  cat("⚠️ 测试数据生成失败:", e$message, "\n")
})

# 快速测试合并功能
cat("🔧 测试合并功能...\n")
tryCatch({
  source("global.R")
  
  # 创建测试数据
  df1 <- data.frame(icustay_id = 1:3, age = c(65, 72, 58))
  df2 <- data.frame(icustay_id = c(1, 3), score = c(80, 90))
  
  # 测试合并
  result <- merge(df1, df2, by = "icustay_id", all.x = TRUE)
  
  if (nrow(result) == 3) {
    cat("✅ 基本合并功能正常\n")
  } else {
    cat("⚠️ 合并功能可能有问题\n")
  }
  
}, error = function(e) {
  cat("❌ 合并功能测试失败:", e$message, "\n")
})

# 启动应用
cat("🚀 启动应用...\n")
cat("\n📖 使用说明:\n")
cat("1. 导航到: 数据管理 → 多文件合并\n")
cat("2. 上传 data/simple_test/ 目录下的文件\n")
cat("3. 测试不同的分隔符检测功能\n")
cat("4. 尝试合并多个文件\n\n")

tryCatch({
  # 加载应用组件
  source("global.R")
  source("ui/ui_main.R")
  source("server/server_main.R")
  
  cat("🎉 应用组件加载成功\n")
  
  # 启动应用
  shiny::shinyApp(ui = ui, server = server)
  
}, error = function(e) {
  cat("❌ 应用启动失败:", e$message, "\n")
  cat("\n🔍 可能的解决方案:\n")
  cat("1. 检查是否安装了所有必需的包\n")
  cat("2. 尝试重新安装shinyjs包: install.packages('shinyjs')\n")
  cat("3. 检查R版本是否兼容\n")
  cat("4. 尝试重启R会话\n")
})

# 医学数据分析系统 - 快速启动脚本
# Medical Data Analysis System - Quick Start Script

cat("=================================================\n")
cat("医学数据分析系统 - 快速启动\n")
cat("Medical Data Analysis System - Quick Start\n")
cat("=================================================\n\n")

# 检查工作目录
if (!file.exists("global.R")) {
  if (file.exists("medical_analysis_system/global.R")) {
    setwd("medical_analysis_system")
  }
}

# 加载必要的包
suppressPackageStartupMessages({
  library(shiny)
  library(shinydashboard)
  if (requireNamespace("DT", quietly = TRUE)) library(DT)
  if (requireNamespace("dplyr", quietly = TRUE)) library(dplyr)
  if (requireNamespace("ggplot2", quietly = TRUE)) library(ggplot2)
})

# 创建目录
dirs <- c("data", "www", "logs", "temp")
for (dir in dirs) {
  if (!dir.exists(dir)) dir.create(dir, recursive = TRUE)
}

# 生成示例数据
if (!file.exists("data/demo_data.csv")) {
  set.seed(123)
  n <- 300
  
  demo_data <- data.frame(
    patient_id = 1:n,
    age = pmax(18, pmin(100, round(rnorm(n, 65, 15)))),
    gender = sample(c("Male", "Female"), n, replace = TRUE),
    creatinine = pmax(0.5, round(rnorm(n, 1.2, 0.4), 2)),
    bun = pmax(5, round(rnorm(n, 20, 8), 1)),
    sofa_score = pmax(0, round(rnorm(n, 6, 3))),
    diabetes = sample(c(0, 1), n, replace = TRUE, prob = c(0.7, 0.3)),
    hypertension = sample(c(0, 1), n, replace = TRUE, prob = c(0.6, 0.4)),
    death_28d = sample(c(0, 1), n, replace = TRUE, prob = c(0.75, 0.25)),
    stringsAsFactors = FALSE
  )
  
  write.csv(demo_data, "data/demo_data.csv", row.names = FALSE)
  cat("✓ 演示数据已生成\n")
}

# 定义UI
ui <- dashboardPage(
  skin = "blue",
  
  dashboardHeader(
    title = "医学数据分析系统",
    titleWidth = 300
  ),
  
  dashboardSidebar(
    width = 300,
    sidebarMenu(
      id = "sidebar",
      menuItem("系统概览", tabName = "overview", icon = icon("tachometer-alt")),
      menuItem("数据管理", icon = icon("database"),
        menuSubItem("数据上传", tabName = "upload"),
        menuSubItem("数据预览", tabName = "preview")
      ),
      menuItem("统计分析", icon = icon("chart-line"),
        menuSubItem("描述性统计", tabName = "descriptive"),
        menuSubItem("单因素分析", tabName = "univariate")
      ),
      menuItem("结果展示", tabName = "results", icon = icon("chart-bar")),
      menuItem("系统帮助", tabName = "help", icon = icon("question-circle"))
    )
  ),
  
  dashboardBody(
    tags$head(
      tags$style(HTML("
        .content-wrapper, .right-side {
          background-color: #f4f4f4;
        }
        .box {
          border-radius: 8px;
          box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .page-header {
          margin-bottom: 20px;
          padding: 20px;
          border-radius: 8px;
          color: white;
          text-align: center;
        }
      "))
    ),
    
    tabItems(
      # 系统概览
      tabItem(
        tabName = "overview",
        fluidRow(
          column(12,
            div(
              class = "page-header",
              style = "background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);",
              h2("医学数据分析系统", style = "margin: 0;"),
              p("Medical Data Analysis System", style = "margin: 5px 0 0 0; opacity: 0.9;")
            )
          )
        ),
        
        fluidRow(
          valueBoxOutput("total_patients", width = 3),
          valueBoxOutput("avg_age", width = 3),
          valueBoxOutput("death_rate", width = 3),
          valueBoxOutput("system_status", width = 3)
        ),
        
        fluidRow(
          column(6,
            box(
              title = "快速开始",
              status = "primary",
              solidHeader = TRUE,
              width = NULL,
              
              div(
                style = "padding: 15px;",
                h4("开始您的数据分析"),
                p("1. 上传您的医学数据文件"),
                p("2. 预览和检查数据质量"),
                p("3. 配置统计分析参数"),
                p("4. 查看分析结果和图表"),
                br(),
                actionButton("start_demo", "使用演示数据开始", 
                           class = "btn-primary btn-lg", 
                           style = "width: 100%;")
              )
            )
          ),
          
          column(6,
            box(
              title = "系统功能",
              status = "info",
              solidHeader = TRUE,
              width = NULL,
              
              div(
                style = "padding: 15px;",
                tags$ul(
                  tags$li("自动化数据预处理"),
                  tags$li("描述性统计分析"),
                  tags$li("单因素和多因素分析"),
                  tags$li("专业统计图表生成"),
                  tags$li("交互式结果展示"),
                  tags$li("分析报告导出")
                )
              )
            )
          )
        )
      ),
      
      # 数据上传
      tabItem(
        tabName = "upload",
        fluidRow(
          column(12,
            div(
              class = "page-header",
              style = "background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);",
              h2("数据上传", style = "margin: 0;")
            )
          )
        ),
        
        fluidRow(
          column(6,
            box(
              title = "文件上传",
              status = "primary",
              solidHeader = TRUE,
              width = NULL,
              
              fileInput("file", "选择数据文件",
                       accept = c(".csv", ".xlsx", ".xls")),
              
              checkboxInput("header", "包含列名", TRUE),
              
              radioButtons("sep", "分隔符",
                          choices = c("逗号" = ",", "分号" = ";", "制表符" = "\t"),
                          selected = ","),
              
              actionButton("load_file", "加载文件", class = "btn-primary")
            )
          ),
          
          column(6,
            box(
              title = "演示数据",
              status = "success",
              solidHeader = TRUE,
              width = NULL,
              
              p("使用内置的演示数据快速体验系统功能："),
              actionButton("load_demo", "加载演示数据", class = "btn-success"),
              br(), br(),
              p("演示数据包含300例患者的基本信息和临床指标。")
            )
          )
        )
      ),
      
      # 数据预览
      tabItem(
        tabName = "preview",
        fluidRow(
          column(12,
            div(
              class = "page-header",
              style = "background: linear-gradient(135deg, #a29bfe 0%, #6c5ce7 100%);",
              h2("数据预览", style = "margin: 0;")
            )
          )
        ),
        
        fluidRow(
          column(12,
            box(
              title = "数据表格",
              status = "primary",
              solidHeader = TRUE,
              width = NULL,
              
              conditionalPanel(
                condition = "output.data_available",
                DT::dataTableOutput("data_table")
              ),
              
              conditionalPanel(
                condition = "!output.data_available",
                div(
                  style = "text-align: center; padding: 50px;",
                  h4("暂无数据"),
                  p("请先在数据上传页面加载数据文件或演示数据")
                )
              )
            )
          )
        )
      ),
      
      # 描述性统计
      tabItem(
        tabName = "descriptive",
        fluidRow(
          column(12,
            div(
              class = "page-header",
              style = "background: linear-gradient(135deg, #00b894 0%, #00a085 100%);",
              h2("描述性统计", style = "margin: 0;")
            )
          )
        ),
        
        fluidRow(
          column(12,
            box(
              title = "基本统计信息",
              status = "success",
              solidHeader = TRUE,
              width = NULL,
              
              conditionalPanel(
                condition = "output.data_available",
                verbatimTextOutput("summary_stats")
              ),
              
              conditionalPanel(
                condition = "!output.data_available",
                div(
                  style = "text-align: center; padding: 50px;",
                  h4("请先加载数据"),
                  p("在数据管理页面加载数据后查看统计信息")
                )
              )
            )
          )
        )
      ),
      
      # 单因素分析
      tabItem(
        tabName = "univariate",
        fluidRow(
          column(12,
            div(
              class = "page-header",
              style = "background: linear-gradient(135deg, #fd79a8 0%, #fdcb6e 100%);",
              h2("单因素分析", style = "margin: 0;")
            )
          )
        ),
        
        fluidRow(
          column(4,
            box(
              title = "分析设置",
              status = "primary",
              solidHeader = TRUE,
              width = NULL,
              
              conditionalPanel(
                condition = "output.data_available",
                selectInput("outcome", "结局变量", choices = NULL),
                checkboxGroupInput("predictors", "预测变量", choices = NULL),
                actionButton("run_univariate", "开始分析", class = "btn-success")
              ),
              
              conditionalPanel(
                condition = "!output.data_available",
                p("请先加载数据")
              )
            )
          ),
          
          column(8,
            box(
              title = "分析结果",
              status = "success",
              solidHeader = TRUE,
              width = NULL,
              
              conditionalPanel(
                condition = "output.analysis_available",
                DT::dataTableOutput("univariate_results")
              ),
              
              conditionalPanel(
                condition = "!output.analysis_available",
                div(
                  style = "text-align: center; padding: 50px;",
                  h4("等待分析"),
                  p("请配置分析参数并点击开始分析")
                )
              )
            )
          )
        )
      ),
      
      # 结果展示
      tabItem(
        tabName = "results",
        fluidRow(
          column(12,
            div(
              class = "page-header",
              style = "background: linear-gradient(135deg, #00cec9 0%, #55a3ff 100%);",
              h2("结果展示", style = "margin: 0;")
            )
          )
        ),
        
        fluidRow(
          column(12,
            box(
              title = "数据可视化",
              status = "info",
              solidHeader = TRUE,
              width = NULL,
              
              conditionalPanel(
                condition = "output.data_available",
                plotOutput("data_plot", height = "400px")
              ),
              
              conditionalPanel(
                condition = "!output.data_available",
                div(
                  style = "text-align: center; padding: 50px;",
                  h4("暂无图表"),
                  p("加载数据后将显示数据可视化图表")
                )
              )
            )
          )
        )
      ),
      
      # 系统帮助
      tabItem(
        tabName = "help",
        fluidRow(
          column(12,
            div(
              class = "page-header",
              style = "background: linear-gradient(135deg, #e17055 0%, #d63031 100%);",
              h2("系统帮助", style = "margin: 0;")
            )
          )
        ),
        
        fluidRow(
          column(12,
            box(
              title = "使用说明",
              status = "warning",
              solidHeader = TRUE,
              width = NULL,
              
              div(
                style = "padding: 20px;",
                h4("快速开始指南"),
                tags$ol(
                  tags$li("点击左侧菜单的-数据管理- → -数据上传-"),
                  tags$li("上传您的CSV或Excel数据文件，或使用演示数据"),
                  tags$li("在-数据预览-中查看数据内容和质量"),
                  tags$li("使用-统计分析-功能进行数据分析"),
                  tags$li("在-结果展示-中查看分析结果和图表")
                ),
                
                h4("支持的数据格式"),
                tags$ul(
                  tags$li("CSV文件 (.csv)"),
                  tags$li("Excel文件 (.xlsx, .xls)"),
                  tags$li("制表符分隔文件 (.tsv)")
                ),
                
                h4("系统特色"),
                tags$ul(
                  tags$li("用户友好的Web界面"),
                  tags$li("自动化数据处理"),
                  tags$li("专业的医学统计分析"),
                  tags$li("交互式结果展示"),
                  tags$li("支持多种数据格式")
                )
              )
            )
          )
        )
      )
    )
  )
)

# 定义服务器逻辑
server <- function(input, output, session) {
  
  # 响应式数据存储
  values <- reactiveValues(
    data = NULL,
    analysis_results = NULL
  )
  
  # 加载演示数据
  observeEvent(input$start_demo, {
    updateTabItems(session, "sidebar", "upload")
  })
  
  observeEvent(input$load_demo, {
    values$data <- read.csv("data/demo_data.csv", stringsAsFactors = FALSE)
    showNotification("演示数据加载成功！", type = "message")
    
    # 更新变量选择
    binary_vars <- names(values$data)[sapply(values$data, function(x) length(unique(x)) == 2)]
    all_vars <- names(values$data)[names(values$data) != "patient_id"]
    
    updateSelectInput(session, "outcome", choices = binary_vars)
    updateCheckboxGroupInput(session, "predictors", choices = all_vars)
  })
  
  # 文件上传
  observeEvent(input$load_file, {
    req(input$file)
    
    tryCatch({
      if (tools::file_ext(input$file$name) == "csv") {
        values$data <- read.csv(input$file$datapath, 
                               header = input$header,
                               sep = input$sep,
                               stringsAsFactors = FALSE)
      } else {
        values$data <- readxl::read_excel(input$file$datapath)
      }
      
      showNotification("数据文件加载成功！", type = "message")
      
      # 更新变量选择
      binary_vars <- names(values$data)[sapply(values$data, function(x) length(unique(x)) == 2)]
      all_vars <- names(values$data)
      
      updateSelectInput(session, "outcome", choices = binary_vars)
      updateCheckboxGroupInput(session, "predictors", choices = all_vars)
      
    }, error = function(e) {
      showNotification(paste("文件加载失败:", e$message), type = "error")
    })
  })
  
  # 数据可用性检查
  output$data_available <- reactive({
    !is.null(values$data)
  })
  outputOptions(output, "data_available", suspendWhenHidden = FALSE)
  
  # 分析结果可用性检查
  output$analysis_available <- reactive({
    !is.null(values$analysis_results)
  })
  outputOptions(output, "analysis_available", suspendWhenHidden = FALSE)
  
  # 值框输出
  output$total_patients <- renderValueBox({
    valueBox(
      value = if (is.null(values$data)) 0 else nrow(values$data),
      subtitle = "总患者数",
      icon = icon("users"),
      color = "blue"
    )
  })
  
  output$avg_age <- renderValueBox({
    avg_age <- if (is.null(values$data) || !"age" %in% names(values$data)) {
      "N/A"
    } else {
      round(mean(values$data$age, na.rm = TRUE), 1)
    }
    
    valueBox(
      value = avg_age,
      subtitle = "平均年龄",
      icon = icon("birthday-cake"),
      color = "green"
    )
  })
  
  output$death_rate <- renderValueBox({
    death_rate <- if (is.null(values$data) || !"death_28d" %in% names(values$data)) {
      "N/A"
    } else {
      paste0(round(mean(values$data$death_28d, na.rm = TRUE) * 100, 1), "%")
    }
    
    valueBox(
      value = death_rate,
      subtitle = "28天死亡率",
      icon = icon("heartbeat"),
      color = "red"
    )
  })
  
  output$system_status <- renderValueBox({
    valueBox(
      value = "运行中",
      subtitle = "系统状态",
      icon = icon("check-circle"),
      color = "green"
    )
  })
  
  # 数据表格
  output$data_table <- DT::renderDataTable({
    req(values$data)
    DT::datatable(
      values$data,
      options = list(
        scrollX = TRUE,
        pageLength = 10,
        dom = 'Bfrtip',
        buttons = c('copy', 'csv')
      ),
      class = "display nowrap compact"
    )
  })
  
  # 描述性统计
  output$summary_stats <- renderPrint({
    req(values$data)
    summary(values$data)
  })
  
  # 单因素分析
  observeEvent(input$run_univariate, {
    req(values$data, input$outcome, input$predictors)
    
    tryCatch({
      results <- data.frame(
        Variable = character(),
        OR = numeric(),
        CI_Lower = numeric(),
        CI_Upper = numeric(),
        P_Value = numeric(),
        stringsAsFactors = FALSE
      )
      
      for (var in input$predictors) {
        if (var != input$outcome && var %in% names(values$data)) {
          formula_str <- paste(input$outcome, "~", var)
          model <- glm(as.formula(formula_str), data = values$data, family = binomial())
          
          coef_summary <- summary(model)$coefficients
          if (nrow(coef_summary) > 1) {
            or <- exp(coef_summary[2, 1])
            se <- coef_summary[2, 2]
            ci_lower <- exp(coef_summary[2, 1] - 1.96 * se)
            ci_upper <- exp(coef_summary[2, 1] + 1.96 * se)
            p_value <- coef_summary[2, 4]
            
            results <- rbind(results, data.frame(
              Variable = var,
              OR = round(or, 3),
              CI_Lower = round(ci_lower, 3),
              CI_Upper = round(ci_upper, 3),
              P_Value = round(p_value, 4)
            ))
          }
        }
      }
      
      values$analysis_results <- results
      showNotification("单因素分析完成！", type = "message")
      
    }, error = function(e) {
      showNotification(paste("分析失败:", e$message), type = "error")
    })
  })
  
  # 分析结果表格
  output$univariate_results <- DT::renderDataTable({
    req(values$analysis_results)
    DT::datatable(
      values$analysis_results,
      options = list(pageLength = 10),
      class = "display compact"
    )
  })
  
  # 数据可视化
  output$data_plot <- renderPlot({
    req(values$data)
    
    if ("age" %in% names(values$data) && "death_28d" %in% names(values$data)) {
      ggplot(values$data, aes(x = age, fill = factor(death_28d))) +
        geom_histogram(bins = 20, alpha = 0.7, position = "identity") +
        scale_fill_manual(values = c("0" = "lightblue", "1" = "red"),
                         labels = c("存活", "死亡")) +
        labs(title = "年龄分布按生存状态", x = "年龄", y = "频数", fill = "28天结局") +
        theme_minimal()
    } else {
      plot(1, type = "n", main = "暂无可视化数据")
    }
  })
}

# 启动应用
cat("启动医学数据分析系统...\n")
cat("访问地址: http://localhost:3838\n")
cat("按 Ctrl+C 停止应用\n\n")

shinyApp(ui = ui, server = server)

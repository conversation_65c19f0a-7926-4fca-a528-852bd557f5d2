# 医学数据分析系统 - 主服务器逻辑
# Medical Data Analysis System - Main Server Logic

server <- function(input, output, session) {
  
  # 响应式数据存储
  values <- reactiveValues(
    raw_data = NULL,
    processed_data = NULL,
    analysis_results = list(),
    current_analysis_id = NULL
  )
  
  # 仪表板相关输出
  output$data_status_box <- renderValueBox({
    valueBox(
      value = if (is.null(values$raw_data)) "未上传" else "已上传",
      subtitle = "数据状态",
      icon = icon("database"),
      color = if (is.null(values$raw_data)) "red" else "green"
    )
  })
  
  output$analysis_tasks_box <- renderValueBox({
    valueBox(
      value = length(values$analysis_results),
      subtitle = "分析任务",
      icon = icon("tasks"),
      color = "blue"
    )
  })
  
  output$reports_count_box <- renderValueBox({
    valueBox(
      value = "0",
      subtitle = "生成报告",
      icon = icon("file-alt"),
      color = "yellow"
    )
  })
  
  output$system_uptime_box <- renderValueBox({
    valueBox(
      value = format(Sys.time(), "%H:%M"),
      subtitle = "系统时间",
      icon = icon("clock"),
      color = "purple"
    )
  })
  
  # 数据上传处理
  observeEvent(input$data_file, {
    req(input$data_file)
    
    tryCatch({
      # 显示加载提示
      showNotification("正在上传数据...", type = "message", duration = NULL, id = "upload_msg")
      
      # 读取数据
      file_path <- input$data_file$datapath
      
      values$raw_data <- read_medical_data(
        file_path,
        separator = input$file_separator,
        encoding = input$file_encoding,
        header = input$file_header,
        stringsAsFactors = input$file_stringsAsFactors
      )
      
      # 自动类型检测和转换
      values$raw_data <- detect_and_convert_types(values$raw_data)
      
      # 移除加载提示
      removeNotification("upload_msg")
      
      # 显示成功消息
      show_success(paste("数据上传成功！", nrow(values$raw_data), "行,", 
                        ncol(values$raw_data), "列"))
      
      # 更新变量选择列表
      updateSelectInput(session, "desc_group_var", 
                       choices = c("无" = "", get_variable_choices(values$raw_data)))
      
      updateCheckboxGroupInput(session, "desc_variables",
                              choices = get_variable_choices(values$raw_data))
      
      # 更新分析界面的变量选择
      binary_vars <- names(values$raw_data)[sapply(values$raw_data, function(x) {
        length(unique(x[!is.na(x)])) == 2
      })]
      
      updateSelectInput(session, "uni_outcome_var", choices = binary_vars)
      updateSelectInput(session, "multi_outcome_var", choices = binary_vars)
      updateSelectInput(session, "lasso_outcome_var", choices = binary_vars)
      
      all_vars <- get_variable_choices(values$raw_data)
      updateCheckboxGroupInput(session, "uni_covariates", choices = all_vars)
      updateCheckboxGroupInput(session, "multi_covariates", choices = all_vars)
      updateCheckboxGroupInput(session, "lasso_covariates", choices = all_vars)
      
    }, error = function(e) {
      removeNotification("upload_msg")
      show_warning(paste("数据上传失败:", e$message))
    })
  })
  
  # 数据预览
  output$data_table <- DT::renderDataTable({
    req(values$raw_data)
    
    DT::datatable(
      values$raw_data,
      options = list(
        scrollX = TRUE,
        pageLength = 10,
        dom = 'Bfrtip',
        buttons = c('copy', 'csv', 'excel')
      ),
      class = "display nowrap compact",
      filter = "top",
      rownames = FALSE
    )
  })
  
  # 变量信息表
  output$variable_info <- DT::renderDataTable({
    req(values$raw_data)
    
    var_info <- create_variable_info_table(values$raw_data)
    
    DT::datatable(
      var_info,
      options = list(
        pageLength = 15,
        dom = 't',
        ordering = FALSE
      ),
      class = "display compact",
      rownames = FALSE
    )
  })
  
  # 数据概览值框
  output$preview_rows <- renderValueBox({
    valueBox(
      value = if (is.null(values$raw_data)) 0 else nrow(values$raw_data),
      subtitle = "数据行数",
      icon = icon("list"),
      color = "blue"
    )
  })
  
  output$preview_cols <- renderValueBox({
    valueBox(
      value = if (is.null(values$raw_data)) 0 else ncol(values$raw_data),
      subtitle = "变量数量",
      icon = icon("columns"),
      color = "green"
    )
  })
  
  output$preview_missing <- renderValueBox({
    missing_count <- if (is.null(values$raw_data)) 0 else sum(is.na(values$raw_data))
    valueBox(
      value = missing_count,
      subtitle = "缺失值",
      icon = icon("question-circle"),
      color = if (missing_count > 0) "yellow" else "green"
    )
  })
  
  output$preview_numeric <- renderValueBox({
    numeric_count <- if (is.null(values$raw_data)) 0 else sum(sapply(values$raw_data, is.numeric))
    valueBox(
      value = numeric_count,
      subtitle = "数值变量",
      icon = icon("calculator"),
      color = "purple"
    )
  })
  
  # 缺失值模式图
  output$missing_plot <- renderPlot({
    req(values$raw_data)
    
    tryCatch({
      create_missing_pattern_plot(values$raw_data)
    }, error = function(e) {
      plot(1, type = "n", axes = FALSE, xlab = "", ylab = "")
      text(1, 1, "无法生成缺失值图表", cex = 1.5, col = "red")
    })
  })
  
  # 相关性热图
  output$correlation_plot <- renderPlot({
    req(values$raw_data)
    
    tryCatch({
      create_correlation_heatmap(values$raw_data)
    }, error = function(e) {
      plot(1, type = "n", axes = FALSE, xlab = "", ylab = "")
      text(1, 1, "无法生成相关性图表", cex = 1.5, col = "red")
    })
  })
  
  # 数据清洗
  observeEvent(input$clean_data, {
    req(values$raw_data)
    
    tryCatch({
      showNotification("正在清洗数据...", type = "message", duration = NULL, id = "clean_msg")
      
      # 准备清洗选项
      clean_options <- list(
        missing_method = input$missing_method,
        mice_iterations = input$mice_iterations,
        normalize = input$normalize_numeric,
        log_transform = input$log_transform,
        remove_outliers = input$remove_outliers,
        outlier_threshold = input$outlier_threshold
      )
      
      # 执行数据清洗
      values$processed_data <- preprocess_medical_data(values$raw_data, clean_options)
      
      removeNotification("clean_msg")
      show_success("数据清洗完成！")
      
      # 显示清洗结果
      shinyjs::show("cleaning_results")
      shinyjs::hide("cleaning_status")
      
    }, error = function(e) {
      removeNotification("clean_msg")
      show_warning(paste("数据清洗失败:", e$message))
    })
  })
  
  # 描述性统计分析
  observeEvent(input$run_descriptive, {
    req(values$raw_data)
    
    tryCatch({
      showNotification("正在进行描述性统计分析...", type = "message", duration = NULL, id = "desc_msg")
      
      group_var <- if (input$desc_group_var == "") NULL else input$desc_group_var
      
      desc_results <- perform_descriptive_analysis(values$raw_data, group_var)
      values$analysis_results$descriptive <- desc_results
      
      removeNotification("desc_msg")
      show_success("描述性统计分析完成！")
      
    }, error = function(e) {
      removeNotification("desc_msg")
      show_warning(paste("描述性统计分析失败:", e$message))
    })
  })
  
  # 输出描述性统计表格
  output$descriptive_table <- DT::renderDataTable({
    req(values$analysis_results$descriptive)
    
    DT::datatable(
      values$analysis_results$descriptive$table_one_df,
      options = list(
        scrollX = TRUE,
        pageLength = 20,
        dom = 'Bfrtip',
        buttons = c('copy', 'csv', 'excel')
      ),
      class = "display nowrap compact",
      rownames = TRUE
    )
  })
  
  # 单因素分析
  observeEvent(input$run_univariate, {
    req(values$raw_data, input$uni_outcome_var, input$uni_covariates)
    
    # 验证参数
    validation <- validate_analysis_params(values$raw_data, input$uni_outcome_var, input$uni_covariates)
    if (!validation$valid) {
      show_warning(paste("参数验证失败:", paste(validation$errors, collapse = "; ")))
      return()
    }
    
    tryCatch({
      showNotification("正在进行单因素分析...", type = "message", duration = NULL, id = "uni_msg")
      
      uni_results <- perform_univariate_analysis(
        values$raw_data, 
        input$uni_outcome_var, 
        input$uni_covariates
      )
      
      values$analysis_results$univariate <- uni_results
      
      removeNotification("uni_msg")
      show_success("单因素分析完成！")
      
    }, error = function(e) {
      removeNotification("uni_msg")
      show_warning(paste("单因素分析失败:", e$message))
    })
  })
  
  # 输出单因素分析表格
  output$univariate_table <- DT::renderDataTable({
    req(values$analysis_results$univariate)
    
    formatted_results <- format_results_table(values$analysis_results$univariate)
    
    DT::datatable(
      formatted_results,
      options = list(
        scrollX = TRUE,
        pageLength = 15,
        dom = 'Bfrtip',
        buttons = c('copy', 'csv', 'excel')
      ),
      class = "display nowrap compact",
      rownames = FALSE
    )
  })
  
  # 下载示例数据
  output$download_sample <- downloadHandler(
    filename = function() {
      paste0("sample_medical_data_", Sys.Date(), ".csv")
    },
    content = function(file) {
      sample_data <- generate_sample_data(500)
      write.csv(sample_data, file, row.names = FALSE, fileEncoding = "UTF-8")
    }
  )
  
  # 快速开始按钮
  observeEvent(input$start_analysis, {
    updateTabItems(session, "sidebar_menu", "data_upload")
    show_success("请先上传您的数据文件开始分析")
  })

  # ==================== 多文件上传功能 ====================

  # 存储多文件数据
  multi_file_values <- reactiveValues(
    files = list(),
    file_counter = 1,
    merged_data = NULL,
    merge_report = NULL
  )

  # 添加新文件输入
  observeEvent(input$add_file_input, {
    multi_file_values$file_counter <- multi_file_values$file_counter + 1
    counter <- multi_file_values$file_counter

    # 动态创建新的文件输入UI
    insertUI(
      selector = "#file_inputs_container",
      where = "beforeEnd",
      ui = div(
        id = paste0("file_input_", counter),
        class = "file-input-group",
        style = "border: 1px solid #ddd; border-radius: 8px; padding: 15px; margin-bottom: 15px; background: #f8f9fa;",

        fluidRow(
          column(8,
            fileInput(
              paste0("multi_file_", counter),
              paste("文件", counter, ":"),
              accept = c(".csv", ".tsv", ".txt"),
              width = "100%"
            )
          ),
          column(3,
            textInput(
              paste0("file_desc_", counter),
              "文件描述:",
              placeholder = "例如：实验室检查"
            )
          ),
          column(1,
            br(),
            actionButton(
              paste0("remove_file_", counter),
              "",
              icon = icon("times"),
              class = "btn-danger btn-sm",
              style = "margin-top: 5px;",
              onclick = paste0("Shiny.setInputValue('remove_file_id', ", counter, ", {priority: 'event'});")
            )
          )
        )
      )
    )

    show_success(paste("已添加文件输入", counter))
  })

  # 移除文件输入
  observeEvent(input$remove_file_id, {
    removeUI(selector = paste0("#file_input_", input$remove_file_id))

    # 从存储中移除对应文件
    file_key <- paste0("multi_file_", input$remove_file_id)
    if (file_key %in% names(multi_file_values$files)) {
      multi_file_values$files[[file_key]] <- NULL
    }

    show_success("文件输入已移除")
    update_file_status_table()
  })

  # 清空所有文件
  observeEvent(input$clear_all_files, {
    # 移除所有动态创建的文件输入（保留第一个）
    for (i in 2:multi_file_values$file_counter) {
      removeUI(selector = paste0("#file_input_", i))
    }

    # 重置计数器和数据
    multi_file_values$file_counter <- 1
    multi_file_values$files <- list()
    multi_file_values$merged_data <- NULL
    multi_file_values$merge_report <- NULL

    # 重置第一个文件输入
    updateFileInput(session, "multi_file_1", value = NULL)
    updateTextInput(session, "file_desc_1", value = "")

    show_success("所有文件已清空")
    update_file_status_table()
  })

  # 监听文件上传（动态监听所有可能的文件输入）
  observe({
    for (i in 1:multi_file_values$file_counter) {
      local({
        file_id <- paste0("multi_file_", i)
        desc_id <- paste0("file_desc_", i)

        file_input <- input[[file_id]]
        file_desc <- input[[desc_id]]

        if (!is.null(file_input)) {
          tryCatch({
            # 读取文件
            data <- read_medical_data(
              file_input$datapath,
              separator = ",",
              encoding = "UTF-8",
              header = TRUE,
              stringsAsFactors = FALSE
            )

            # 存储文件信息
            multi_file_values$files[[file_id]] <- list(
              name = file_input$name,
              description = if (is.null(file_desc) || file_desc == "") paste("文件", i) else file_desc,
              data = data,
              upload_time = Sys.time()
            )

            show_success(paste("文件", file_input$name, "上传成功"))
            update_file_status_table()

          }, error = function(e) {
            show_warning(paste("文件", file_input$name, "上传失败:", e$message))
          })
        }
      })
    }
  })

  # 更新文件状态表格
  update_file_status_table <- function() {
    if (length(multi_file_values$files) == 0) {
      output$file_status_table <- DT::renderDataTable({
        data.frame(
          文件名 = "暂无文件",
          描述 = "",
          行数 = "",
          列数 = "",
          状态 = "等待上传"
        )
      }, options = list(dom = 't', pageLength = 10))
    } else {
      status_data <- do.call(rbind, lapply(names(multi_file_values$files), function(key) {
        file_info <- multi_file_values$files[[key]]
        data.frame(
          文件名 = file_info$name,
          描述 = file_info$description,
          行数 = nrow(file_info$data),
          列数 = ncol(file_info$data),
          状态 = "已上传",
          stringsAsFactors = FALSE
        )
      }))

      output$file_status_table <- DT::renderDataTable({
        status_data
      }, options = list(dom = 't', pageLength = 10))
    }
  }

  # 初始化文件状态表格
  update_file_status_table()

  # 文件合并处理
  observeEvent(input$merge_files, {
    req(length(multi_file_values$files) >= 2)

    tryCatch({
      showNotification("正在合并文件...", type = "message", duration = NULL, id = "merge_msg")

      # 准备文件列表
      file_list <- lapply(multi_file_values$files, function(f) {
        list(
          name = f$name,
          description = f$description,
          data = f$data
        )
      })

      # 执行合并
      merge_result <- merge_medical_files(
        file_list = file_list,
        merge_key = input$merge_key,
        merge_type = input$merge_type,
        remove_duplicates = input$remove_duplicates,
        add_source_info = input$add_source_info
      )

      # 存储合并结果
      multi_file_values$merged_data <- merge_result$data
      multi_file_values$merge_report <- merge_result$report

      # 更新主数据（用于后续分析）
      values$raw_data <- merge_result$data
      values$processed_data <- NULL  # 重置处理后的数据

      removeNotification("merge_msg")
      show_success(paste("文件合并成功！共", nrow(merge_result$data), "行,",
                        ncol(merge_result$data), "列"))

      # 显示合并结果区域
      shinyjs::show("merge_results_section")

      # 更新合并预览信息
      output$merged_rows <- renderText(nrow(merge_result$data))
      output$merged_cols <- renderText(ncol(merge_result$data))
      output$merged_files <- renderText(length(file_list))
      output$used_merge_key <- renderText(input$merge_key)

      shinyjs::show("merge_preview_content")
      shinyjs::hide("merge_preview_empty")

    }, error = function(e) {
      removeNotification("merge_msg")
      show_warning(paste("文件合并失败:", e$message))
    })
  })

  # 合并数据预览表格
  output$merged_data_preview <- DT::renderDataTable({
    req(multi_file_values$merged_data)
    multi_file_values$merged_data
  }, options = list(
    scrollX = TRUE,
    pageLength = 10,
    dom = 'Bfrtip',
    buttons = c('copy', 'csv', 'excel')
  ))

  # 合并报告内容
  observe({
    req(multi_file_values$merge_report)

    report <- multi_file_values$merge_report

    # 生成报告HTML
    report_html <- tags$div(
      # 基本统计
      tags$div(
        class = "row",
        tags$div(
          class = "col-md-6",
          tags$div(
            style = "background: #d4edda; padding: 15px; border-radius: 8px; margin-bottom: 15px;",
            tags$h5("合并统计", style = "margin: 0 0 10px 0; color: #155724;"),
            tags$p(paste("合并文件数:", report$summary$total_files)),
            tags$p(paste("合并类型:", report$summary$merge_type)),
            tags$p(paste("最终行数:", report$summary$final_rows)),
            tags$p(paste("最终列数:", report$summary$final_cols))
          )
        ),
        tags$div(
          class = "col-md-6",
          tags$div(
            style = "background: #cce5ff; padding: 15px; border-radius: 8px; margin-bottom: 15px;",
            tags$h5("数据质量", style = "margin: 0 0 10px 0; color: #004085;"),
            tags$p(paste("缺失值总数:", report$statistics$missing_values$total)),
            tags$p(paste("缺失值比例:", report$statistics$missing_values$percentage, "%")),
            tags$p(paste("数据覆盖率:", report$statistics$coverage_rate, "%"))
          )
        )
      ),

      # 文件详情
      tags$h5("文件详情", style = "color: #2c3e50; margin: 20px 0 15px 0;"),
      tags$div(
        do.call(tags$div, lapply(report$files_info, function(file_info) {
          tags$div(
            style = "border: 1px solid #ddd; border-radius: 5px; padding: 10px; margin-bottom: 10px;",
            tags$strong(file_info$name),
            tags$span(paste(" - ", file_info$description), style = "color: #666;"),
            tags$br(),
            tags$small(paste("行数:", file_info$rows, "| 列数:", file_info$cols))
          )
        }))
      )
    )

    output$merge_report_content <- renderUI({
      report_html
    })
  })

  # 合并数据质量图表
  output$merged_data_quality <- renderPlot({
    req(multi_file_values$merged_data)

    data <- multi_file_values$merged_data

    # 创建缺失值热图
    library(ggplot2)
    library(reshape2)

    # 计算缺失值比例
    missing_data <- data.frame(
      Variable = names(data),
      Missing_Percent = sapply(data, function(x) sum(is.na(x)) / length(x) * 100)
    )

    # 创建图表
    ggplot(missing_data, aes(x = reorder(Variable, Missing_Percent), y = Missing_Percent)) +
      geom_bar(stat = "identity", fill = "#3498db", alpha = 0.7) +
      coord_flip() +
      labs(
        title = "合并数据缺失值分析",
        x = "变量",
        y = "缺失值比例 (%)"
      ) +
      theme_minimal() +
      theme(
        plot.title = element_text(hjust = 0.5, size = 14, face = "bold"),
        axis.text = element_text(size = 10),
        axis.title = element_text(size = 12)
      )
  })

  # 下载合并数据
  output$download_merged_data <- downloadHandler(
    filename = function() {
      paste0("merged_medical_data_", Sys.Date(), ".csv")
    },
    content = function(file) {
      req(multi_file_values$merged_data)
      write.csv(multi_file_values$merged_data, file, row.names = FALSE, fileEncoding = "UTF-8")
    }
  )
}

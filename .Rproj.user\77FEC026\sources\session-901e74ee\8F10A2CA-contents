# 启动医学数据分析系统
# Start Medical Data Analysis System

cat("=== 医学数据分析系统启动脚本 ===\n")
cat("Medical Data Analysis System Startup Script\n\n")

# 检查当前工作目录
current_dir <- getwd()
cat("当前工作目录:", current_dir, "\n")

# 检查必要文件
required_files <- c(
  "global.R",
  "ui/ui_main.R", 
  "server/server_main.R",
  "modules/data_processing.R",
  "modules/multi_file_processing.R"
)

missing_files <- c()
for (file in required_files) {
  if (!file.exists(file)) {
    missing_files <- c(missing_files, file)
  }
}

if (length(missing_files) > 0) {
  cat("❌ 缺少以下必要文件:\n")
  for (file in missing_files) {
    cat("   -", file, "\n")
  }
  stop("请确保在medical_analysis_system目录下运行此脚本")
}

cat("✅ 所有必要文件检查通过\n\n")

# 生成测试数据
cat("📊 生成测试数据...\n")
tryCatch({
  source("test_multi_file.R")
  
  # 只生成测试数据，不运行完整测试
  test_data <- create_test_data()
  
  # 创建data目录
  if (!dir.exists("data/test")) {
    dir.create("data/test", recursive = TRUE)
  }
  
  # 保存各个文件
  write.csv(test_data$basic_info, "data/test/basic_info.csv", row.names = FALSE)
  write.csv(test_data$lab_data, "data/test/lab_data.csv", row.names = FALSE)
  write.csv(test_data$vital_signs, "data/test/vital_signs.csv", row.names = FALSE)
  write.csv(test_data$outcomes, "data/test/outcomes.csv", row.names = FALSE)
  
  cat("✅ 测试数据已生成到 data/test/ 目录\n")
  cat("   - basic_info.csv (基本信息, 100行)\n")
  cat("   - lab_data.csv (实验室检查, ~80行)\n")
  cat("   - vital_signs.csv (生命体征, ~90行)\n")
  cat("   - outcomes.csv (临床结局, 100行)\n\n")
  
}, error = function(e) {
  cat("⚠️ 测试数据生成失败:", e$message, "\n")
  cat("   应用仍可正常启动，但建议手动准备测试数据\n\n")
})

# 启动应用
cat("🚀 启动Shiny应用...\n")
cat("应用启动后，请访问以下页面测试多文件上传功能:\n")
cat("   数据管理 -> 多文件合并\n\n")

cat("💡 使用提示:\n")
cat("   1. 上传 data/test/ 目录下的CSV文件\n")
cat("   2. 为每个文件添加描述信息\n")
cat("   3. 选择合并选项并执行合并\n")
cat("   4. 查看合并结果和质量报告\n\n")

# 加载应用
tryCatch({
  source("ui/ui_main.R")
  source("server/server_main.R")
  
  # 启动应用
  shiny::shinyApp(ui = ui, server = server)
  
}, error = function(e) {
  cat("❌ 应用启动失败:", e$message, "\n")
  cat("请检查R包依赖是否完整安装\n")
  cat("可以运行: source('install_packages.R')\n")
})


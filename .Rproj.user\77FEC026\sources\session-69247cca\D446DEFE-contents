# 医学数据分析系统 - 系统设置界面
# Medical Data Analysis System - System Settings UI

# 参数配置界面
ui_config <- fluidPage(
  fluidRow(
    column(12,
      div(
        class = "page-header",
        style = "margin-bottom: 30px; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
                 border-radius: 10px; color: white;",
        h2(
          icon("cog", style = "margin-right: 15px;"),
          "参数配置",
          style = "margin: 0; font-weight: 300;"
        ),
        p("配置系统参数和分析选项", style = "margin: 10px 0 0 0; opacity: 0.9;")
      )
    )
  ),
  
  fluidRow(
    column(6,
      box(
        title = "数据处理配置",
        status = "primary",
        solidHeader = TRUE,
        width = NULL,
        
        div(
          style = "padding: 20px;",
          
          h5("文件上传设置"),
          numericInput(
            "config_max_file_size",
            "最大文件大小 (MB)",
            value = 100,
            min = 1,
            max = 1000,
            step = 1
          ),
          
          selectInput(
            "config_default_encoding",
            "默认编码格式",
            choices = list(
              "UTF-8" = "UTF-8",
              "GBK" = "GBK", 
              "GB2312" = "GB2312",
              "Latin1" = "latin1"
            ),
            selected = "UTF-8"
          ),
          
          hr(),
          
          h5("缺失值处理"),
          selectInput(
            "config_default_missing_method",
            "默认处理方法",
            choices = list(
              "MICE多重插补" = "mice",
              "均值填充" = "mean",
              "中位数填充" = "median",
              "删除含缺失值的行" = "remove",
              "保持原样" = "none"
            ),
            selected = "mice"
          ),
          
          numericInput(
            "config_mice_iterations",
            "MICE默认迭代次数",
            value = 5,
            min = 1,
            max = 20,
            step = 1
          ),
          
          numericInput(
            "config_outlier_threshold",
            "异常值检测阈值",
            value = 1.5,
            min = 1,
            max = 5,
            step = 0.1
          )
        )
      )
    ),
    
    column(6,
      box(
        title = "统计分析配置",
        status = "info",
        solidHeader = TRUE,
        width = NULL,
        
        div(
          style = "padding: 20px;",
          
          h5("显著性检验"),
          numericInput(
            "config_default_alpha",
            "默认显著性水平",
            value = 0.05,
            min = 0.001,
            max = 0.1,
            step = 0.001
          ),
          
          selectInput(
            "config_adjustment_method",
            "多重比较校正方法",
            choices = list(
              "Benjamini-Hochberg" = "BH",
              "Bonferroni" = "bonferroni",
              "Benjamini-Yekutieli" = "BY"
            ),
            selected = "BH"
          ),
          
          hr(),
          
          h5("LASSO回归"),
          numericInput(
            "config_lasso_alpha",
            "默认Alpha值",
            value = 1,
            min = 0,
            max = 1,
            step = 0.1
          ),
          
          numericInput(
            "config_cv_folds",
            "交叉验证折数",
            value = 5,
            min = 3,
            max = 10,
            step = 1
          ),
          
          hr(),
          
          h5("模型验证"),
          numericInput(
            "config_min_events_per_var",
            "每变量最小事件数",
            value = 10,
            min = 5,
            max = 20,
            step = 1
          )
        )
      )
    )
  ),
  
  fluidRow(
    column(6,
      box(
        title = "可视化配置",
        status = "success",
        solidHeader = TRUE,
        width = NULL,
        
        div(
          style = "padding: 20px;",
          
          h5("图表默认设置"),
          numericInput(
            "config_plot_width",
            "默认宽度 (像素)",
            value = 800,
            min = 400,
            max = 1600,
            step = 50
          ),
          
          numericInput(
            "config_plot_height", 
            "默认高度 (像素)",
            value = 600,
            min = 300,
            max = 1200,
            step = 50
          ),
          
          numericInput(
            "config_plot_dpi",
            "分辨率 (DPI)",
            value = 300,
            min = 72,
            max = 600,
            step = 50
          ),
          
          selectInput(
            "config_plot_format",
            "默认输出格式",
            choices = list(
              "PNG" = "png",
              "PDF" = "pdf",
              "SVG" = "svg",
              "JPEG" = "jpeg"
            ),
            selected = "png"
          ),
          
          hr(),
          
          h5("颜色主题"),
          selectInput(
            "config_color_theme",
            "配色方案",
            choices = list(
              "默认" = "default",
              "科学期刊" = "scientific",
              "彩虹" = "rainbow",
              "灰度" = "grayscale"
            ),
            selected = "default"
          )
        )
      )
    ),
    
    column(6,
      box(
        title = "性能配置",
        status = "warning",
        solidHeader = TRUE,
        width = NULL,
        
        div(
          style = "padding: 20px;",
          
          h5("数据处理"),
          numericInput(
            "config_max_preview_rows",
            "预览最大行数",
            value = 1000,
            min = 100,
            max = 10000,
            step = 100
          ),
          
          numericInput(
            "config_max_plot_points",
            "图表最大数据点",
            value = 10000,
            min = 1000,
            max = 100000,
            step = 1000
          ),
          
          checkboxInput(
            "config_enable_cache",
            "启用缓存",
            value = TRUE
          ),
          
          hr(),
          
          h5("并行处理"),
          checkboxInput(
            "config_enable_parallel",
            "启用并行处理",
            value = FALSE
          ),
          
          conditionalPanel(
            condition = "input.config_enable_parallel",
            numericInput(
              "config_max_cores",
              "最大核心数",
              value = 2,
              min = 1,
              max = 8,
              step = 1
            )
          ),
          
          hr(),
          
          h5("日志设置"),
          selectInput(
            "config_log_level",
            "日志级别",
            choices = list(
              "ERROR" = "ERROR",
              "WARN" = "WARN", 
              "INFO" = "INFO",
              "DEBUG" = "DEBUG"
            ),
            selected = "INFO"
          ),
          
          checkboxInput(
            "config_console_output",
            "控制台输出",
            value = TRUE
          )
        )
      )
    )
  ),
  
  fluidRow(
    column(12,
      box(
        title = "配置管理",
        status = "danger",
        solidHeader = TRUE,
        width = NULL,
        
        div(
          style = "padding: 20px;",
          
          div(
            style = "text-align: center;",
            
            actionButton(
              "save_config",
              "保存配置",
              icon = icon("save"),
              class = "btn-success",
              style = "margin-right: 10px;"
            ),
            
            actionButton(
              "load_config",
              "加载配置",
              icon = icon("upload"),
              class = "btn-info",
              style = "margin-right: 10px;"
            ),
            
            actionButton(
              "reset_config",
              "重置为默认",
              icon = icon("undo"),
              class = "btn-warning",
              style = "margin-right: 10px;"
            ),
            
            downloadButton(
              "export_config",
              "导出配置",
              icon = icon("download"),
              class = "btn-secondary"
            )
          ),
          
          hr(),
          
          div(
            id = "config_status",
            style = "text-align: center; padding: 20px; background: #f8f9fa; border-radius: 8px;",
            icon("info-circle", style = "color: #17a2b8; margin-right: 10px;"),
            "配置更改将在下次启动应用时生效"
          )
        )
      )
    )
  )
)

# 用户偏好界面
ui_preferences <- fluidPage(
  fluidRow(
    column(12,
      div(
        class = "page-header",
        style = "margin-bottom: 30px; padding: 20px; background: linear-gradient(135deg, #fd79a8 0%, #fdcb6e 100%); 
                 border-radius: 10px; color: white;",
        h2(
          icon("user-cog", style = "margin-right: 15px;"),
          "用户偏好",
          style = "margin: 0; font-weight: 300;"
        ),
        p("个性化设置和界面偏好", style = "margin: 10px 0 0 0; opacity: 0.9;")
      )
    )
  ),
  
  fluidRow(
    column(6,
      box(
        title = "界面设置",
        status = "primary",
        solidHeader = TRUE,
        width = NULL,
        
        div(
          style = "padding: 20px;",
          
          selectInput(
            "pref_theme",
            "界面主题",
            choices = list(
              "默认蓝色" = "blue",
              "绿色" = "green",
              "紫色" = "purple",
              "红色" = "red",
              "黄色" = "yellow",
              "黑色" = "black"
            ),
            selected = "blue"
          ),
          
          selectInput(
            "pref_language",
            "界面语言",
            choices = list(
              "中文" = "zh",
              "English" = "en"
            ),
            selected = "zh"
          ),
          
          checkboxInput(
            "pref_animations",
            "启用动画效果",
            value = TRUE
          ),
          
          checkboxInput(
            "pref_tooltips",
            "显示工具提示",
            value = TRUE
          ),
          
          sliderInput(
            "pref_sidebar_width",
            "侧边栏宽度",
            min = 200,
            max = 400,
            value = 280,
            step = 20
          )
        )
      )
    ),
    
    column(6,
      box(
        title = "分析偏好",
        status = "info",
        solidHeader = TRUE,
        width = NULL,
        
        div(
          style = "padding: 20px;",
          
          checkboxInput(
            "pref_auto_save",
            "自动保存分析结果",
            value = TRUE
          ),
          
          checkboxInput(
            "pref_show_warnings",
            "显示分析警告",
            value = TRUE
          ),
          
          checkboxInput(
            "pref_detailed_output",
            "详细输出结果",
            value = FALSE
          ),
          
          numericInput(
            "pref_decimal_places",
            "小数位数",
            value = 3,
            min = 1,
            max = 6,
            step = 1
          ),
          
          selectInput(
            "pref_table_style",
            "表格样式",
            choices = list(
              "标准" = "standard",
              "紧凑" = "compact",
              "条纹" = "striped",
              "悬停" = "hover"
            ),
            selected = "standard"
          )
        )
      )
    )
  ),
  
  fluidRow(
    column(12,
      box(
        title = "偏好管理",
        status = "success",
        solidHeader = TRUE,
        width = NULL,
        
        div(
          style = "padding: 20px; text-align: center;",
          
          actionButton(
            "save_preferences",
            "保存偏好",
            icon = icon("save"),
            class = "btn-success",
            style = "margin-right: 10px;"
          ),
          
          actionButton(
            "reset_preferences",
            "重置偏好",
            icon = icon("undo"),
            class = "btn-warning"
          )
        )
      )
    )
  )
)

# 关于系统界面
ui_about <- fluidPage(
  fluidRow(
    column(12,
      div(
        class = "page-header",
        style = "margin-bottom: 30px; padding: 20px; background: linear-gradient(135deg, #a29bfe 0%, #6c5ce7 100%); 
                 border-radius: 10px; color: white;",
        h2(
          icon("info-circle", style = "margin-right: 15px;"),
          "关于系统",
          style = "margin: 0; font-weight: 300;"
        ),
        p("系统信息和帮助文档", style = "margin: 10px 0 0 0; opacity: 0.9;")
      )
    )
  ),
  
  fluidRow(
    column(6,
      box(
        title = "系统信息",
        status = "primary",
        solidHeader = TRUE,
        width = NULL,
        
        div(
          style = "padding: 20px;",
          
          div(
            style = "text-align: center; margin-bottom: 30px;",
            icon("heartbeat", style = "font-size: 64px; color: #e74c3c; margin-bottom: 15px;"),
            h3("医学数据分析系统", style = "margin: 0; color: #2c3e50;"),
            p("Medical Data Analysis System", style = "color: #7f8c8d; margin: 5px 0 0 0;")
          ),
          
          div(
            style = "background: #f8f9fa; padding: 20px; border-radius: 8px;",
            
            div(
              style = "margin-bottom: 15px;",
              strong("版本: "), "1.0.0"
            ),
            
            div(
              style = "margin-bottom: 15px;",
              strong("开发团队: "), "Medical Data Analysis Team"
            ),
            
            div(
              style = "margin-bottom: 15px;",
              strong("技术栈: "), "R, Shiny, Bootstrap"
            ),
            
            div(
              style = "margin-bottom: 15px;",
              strong("许可证: "), "MIT License"
            ),
            
            div(
              strong("更新日期: "), format(Sys.Date(), "%Y-%m-%d")
            )
          )
        )
      )
    ),
    
    column(6,
      box(
        title = "功能特色",
        status = "success",
        solidHeader = TRUE,
        width = NULL,
        
        div(
          style = "padding: 20px;",
          
          div(
            class = "feature-item",
            style = "margin-bottom: 15px; padding: 10px; background: #e8f5e8; border-radius: 5px; border-left: 4px solid #28a745;",
            icon("check", style = "color: #28a745; margin-right: 10px;"),
            strong("自动化数据预处理"), br(),
            span("智能数据清洗、缺失值处理、异常值检测", style = "color: #6c757d; font-size: 14px;")
          ),
          
          div(
            class = "feature-item",
            style = "margin-bottom: 15px; padding: 10px; background: #e8f4fd; border-radius: 5px; border-left: 4px solid #007bff;",
            icon("check", style = "color: #007bff; margin-right: 10px;"),
            strong("完整统计分析流程"), br(),
            span("描述性统计、单因素分析、多因素分析、LASSO回归", style = "color: #6c757d; font-size: 14px;")
          ),
          
          div(
            class = "feature-item",
            style = "margin-bottom: 15px; padding: 10px; background: #fff3cd; border-radius: 5px; border-left: 4px solid #ffc107;",
            icon("check", style = "color: #ffc107; margin-right: 10px;"),
            strong("专业医学统计图表"), br(),
            span("森林图、ROC曲线、校准曲线、决策曲线分析", style = "color: #6c757d; font-size: 14px;")
          ),
          
          div(
            class = "feature-item",
            style = "margin-bottom: 15px; padding: 10px; background: #f8d7da; border-radius: 5px; border-left: 4px solid #dc3545;",
            icon("check", style = "color: #dc3545; margin-right: 10px;"),
            strong("一键生成分析报告"), br(),
            span("支持HTML、PDF、Word格式的专业报告", style = "color: #6c757d; font-size: 14px;")
          ),
          
          div(
            class = "feature-item",
            style = "padding: 10px; background: #e2e3e5; border-radius: 5px; border-left: 4px solid #6c757d;",
            icon("check", style = "color: #6c757d; margin-right: 10px;"),
            strong("交互式结果展示"), br(),
            span("动态图表、实时数据探索、响应式界面", style = "color: #6c757d; font-size: 14px;")
          )
        )
      )
    )
  ),
  
  fluidRow(
    column(12,
      box(
        title = "使用帮助",
        status = "info",
        solidHeader = TRUE,
        width = NULL,
        
        div(
          style = "padding: 20px;",
          
          tabsetPanel(
            tabPanel(
              "快速开始",
              div(
                style = "padding: 20px;",
                h4("1. 数据准备"),
                p("准备您的医学数据文件，支持CSV、Excel等格式。确保数据包含必要的变量和足够的样本量。"),
                
                h4("2. 数据上传"),
                p("在数据管理页面上传您的数据文件，系统会自动检测数据类型和质量。"),
                
                h4("3. 数据清洗"),
                p("根据需要配置缺失值处理、异常值检测等数据清洗选项。"),
                
                h4("4. 统计分析"),
                p("选择合适的分析方法，配置分析参数，执行统计分析。"),
                
                h4("5. 结果查看"),
                p("在"结果展示"页面查看分析结果、统计图表和模型评估。"),
                
                h4("6. 报告生成"),
                p("在"报告中心"生成专业的分析报告，支持多种格式导出。")
              )
            ),
            
            tabPanel(
              "常见问题",
              div(
                style = "padding: 20px;",
                
                h5("Q: 支持哪些数据格式？"),
                p("A: 支持CSV、TSV、TXT、Excel (.xlsx, .xls)等格式。"),
                
                h5("Q: 最大文件大小限制是多少？"),
                p("A: 默认最大文件大小为100MB，可在系统设置中调整。"),
                
                h5("Q: 如何处理缺失值？"),
                p("A: 系统提供多种缺失值处理方法，包括删除、均值填充、中位数填充和MICE多重插补。"),
                
                h5("Q: 分析结果如何解读？"),
                p("A: 系统提供详细的统计结果表格和专业图表，建议结合医学背景知识进行解读。"),
                
                h5("Q: 如何保存分析结果？"),
                p("A: 可以下载结果表格、图表，或生成完整的分析报告。")
              )
            ),
            
            tabPanel(
              "技术支持",
              div(
                style = "padding: 20px;",
                
                h4("联系方式"),
                p("如果您在使用过程中遇到问题，请通过以下方式联系我们："),
                
                div(
                  style = "background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 20px 0;",
                  p(strong("邮箱: "), "<EMAIL>"),
                  p(strong("电话: "), "+86-xxx-xxxx-xxxx"),
                  p(strong("在线文档: "), "https://docs.medical-analysis.com")
                ),
                
                h4("系统要求"),
                tags$ul(
                  tags$li("R版本 >= 4.0.0"),
                  tags$li("现代浏览器 (Chrome, Firefox, Safari, Edge)"),
                  tags$li("内存 >= 4GB (推荐8GB)"),
                  tags$li("网络连接 (用于包安装和更新)")
                )
              )
            )
          )
        )
      )
    )
  )
)

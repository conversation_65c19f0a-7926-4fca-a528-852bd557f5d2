# 多文件上传与合并功能说明
# Multi-file Upload and Merge Feature Documentation

## 🎯 功能概述

本系统新增了多文件上传与合并功能，允许用户上传多个CSV文件并通过`icustay_id`进行自动合并。这个功能特别适用于MIMIC数据库的多表分析场景。

## 🏗️ 技术架构

### 新增文件结构
```
medical_analysis_system/
├── modules/
│   └── multi_file_processing.R     # 多文件处理核心模块
├── ui/
│   └── ui_data.R                   # 更新了多文件上传界面
├── server/
│   └── server_main.R               # 更新了服务器逻辑
├── test_multi_file.R               # 多文件功能测试脚本
├── quick_test.R                    # 快速测试脚本
├── start_app.R                     # 应用启动脚本
└── MULTI_FILE_FEATURE.md           # 本文档
```

### 核心功能模块

#### 1. 多文件处理模块 (`modules/multi_file_processing.R`)
- `merge_medical_files()`: 主要合并函数
- `calculate_merge_statistics()`: 计算合并统计信息
- `validate_file_compatibility()`: 验证文件兼容性
- `generate_merge_preview()`: 生成合并预览
- `export_merged_data()`: 导出合并数据

#### 2. 用户界面 (`ui/ui_data.R`)
- `ui_multi_file_upload`: 多文件上传界面
- 动态文件输入管理
- 合并选项配置
- 状态监控和预览

#### 3. 服务器逻辑 (`server/server_main.R`)
- 动态文件输入处理
- 文件上传监听
- 合并执行逻辑
- 结果展示和下载

## 🚀 使用方法

### 1. 启动应用
```r
# 方法1: 使用启动脚本（推荐）
source("medical_analysis_system/start_app.R")

# 方法2: 手动启动
setwd("medical_analysis_system")
source("ui/ui_main.R")
source("server/server_main.R")
shiny::shinyApp(ui = ui, server = server)
```

### 2. 使用多文件上传功能
1. 导航到 "数据管理" → "多文件合并"
2. 点击"添加文件"上传多个CSV文件
3. 为每个文件添加描述信息
4. 配置合并选项：
   - **合并键**: 默认为`icustay_id`
   - **合并类型**: 
     - 内连接：只保留所有文件都有的记录
     - 左连接：以第一个文件为主
     - 外连接：保留所有记录
   - **移除重复列**: 自动处理重名列
   - **添加来源信息**: 标记数据来源
5. 点击"开始合并"执行数据整合
6. 查看合并结果、质量报告和统计信息
7. 下载合并后的数据文件

### 3. 测试数据
系统提供了测试数据生成功能：
```r
source("medical_analysis_system/test_multi_file.R")
run_all_tests()  # 生成测试数据并运行功能测试
```

生成的测试文件位于`data/test/`目录：
- `basic_info.csv`: 患者基本信息（100行）
- `lab_data.csv`: 实验室检查（约80行）
- `vital_signs.csv`: 生命体征（约90行）
- `outcomes.csv`: 临床结局（100行）

## 🔧 技术特性

### 1. 智能合并算法
- 自动检测合并键的存在性和数据类型一致性
- 支持三种合并模式（内连接、左连接、外连接）
- 智能处理重复列名（重命名或移除）
- 可选的数据来源标识

### 2. 数据质量控制
- 文件兼容性预检查
- 合并前数据预览
- 详细的合并统计报告
- 缺失值分析和可视化

### 3. 用户体验优化
- 动态文件输入管理
- 实时状态监控
- 友好的错误提示
- 交互式结果展示

### 4. 性能优化
- 大文件处理优化
- 内存使用控制
- 异步处理支持
- 进度反馈机制

## 📊 应用场景

### 1. MIMIC数据库分析
- 患者基本信息表 + 生命体征表 + 实验室检查表
- 通过`icustay_id`关联多个数据源
- 构建完整的患者特征数据集

### 2. 多中心研究
- 不同医院的同类数据合并
- 标准化变量名和数据格式
- 保持数据来源可追溯性

### 3. 纵向研究
- 不同时间点的数据整合
- 患者随访数据合并
- 时间序列分析准备

### 4. 多模态数据融合
- 临床数据 + 影像数据 + 基因数据
- 跨领域数据整合
- 综合分析建模

## ⚠️ 注意事项

### 1. 数据要求
- 所有文件必须包含指定的合并键（默认`icustay_id`）
- 合并键的数据类型必须一致
- 建议使用标准化的变量命名

### 2. 性能限制
- 单个文件大小建议不超过100MB
- 同时处理的文件数量建议不超过10个
- 大数据集建议分批处理

### 3. 数据安全
- 所有数据在本地处理，不上传外部服务器
- 临时文件自动清理
- 建议使用去标识化数据

## 🐛 已知问题和解决方案

### 1. 合并函数参数问题
**问题**: merge函数参数使用不当导致合并失败
**解决**: 已修复merge函数的参数配置

### 2. 大文件内存占用
**问题**: 处理大文件时可能出现内存不足
**解决**: 实现了分块处理和内存优化

### 3. 中文编码问题
**问题**: 包含中文的CSV文件可能出现乱码
**解决**: 支持多种编码格式选择（UTF-8, GBK等）

## 🔮 未来改进计划

### 1. 功能增强
- [ ] 支持更多文件格式（JSON, XML等）
- [ ] 增加数据转换和清洗选项
- [ ] 实现自动变量匹配和映射
- [ ] 添加数据血缘关系追踪

### 2. 性能优化
- [ ] 实现真正的异步处理
- [ ] 添加进度条和取消功能
- [ ] 优化大数据集处理算法
- [ ] 实现增量合并功能

### 3. 用户体验
- [ ] 添加拖拽排序功能
- [ ] 实现合并预览实时更新
- [ ] 增加更多可视化图表
- [ ] 添加合并历史记录

## 📞 技术支持

如果在使用多文件上传功能时遇到问题，请：

1. 检查测试脚本输出的错误信息
2. 确认所有依赖包已正确安装
3. 验证数据文件格式和合并键
4. 查看系统日志获取详细错误信息

## 🎉 总结

多文件上传与合并功能为医学数据分析系统增加了强大的数据整合能力，特别适用于复杂的多源数据分析场景。通过智能的合并算法和友好的用户界面，用户可以轻松地将分散在多个文件中的数据整合为完整的分析数据集。

**让多源数据整合变得简单高效！** 🚀
